APP_NAME='Tubitak Efficiency Challenge'
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=efficiency
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=cookie
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.eu.sparkpostmail.com
MAIL_PORT=587
MAIL_USERNAME=SMTP_Injection
MAIL_PASSWORD=pass
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS='hello@localhost'
MAIL_FROM_NAME='Efficiency Challenge'
SENT_REGISTER_MAIL=false

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


API_DEFAULT_FORMAT=json
API_STANDARDS_TREE=vnd
API_SUBTYPE=myapi
API_DOMAIN=localhost
#API_PREFIX=api
API_VERSION=v1
API_NAME="MyAPI"
API_STRICT=false
API_DEBUG=false

JWT_SECRET=foobar
JWT_TTL=600

FRONTEND_ADDRESS=http://localhost:4200
VERIFY_EMAIL_WRAPPER='http://localhost:4200/verify-email/%url%'
RESET_PASSWORD_WRAPPER='http://localhost:4200/reset-password/%url%'
APP_TYPE='normal'
#APP_TYPE='lise'

HES_SERVICE_WSDL=https://wsbroker.tubitak.gov.tr/wsbroker/TBTKHESKodServisFW?wsdl
HES_SERVICE_NS=http://tr.gov.tubitak.wsbroker.tbtkheskodservicefw
HES_SERVICE_USERNAME=EC_HES_WS_USER
HES_SERVICE_PASSWORD=PASSWORD_HERE
HES_SERVICE_ORIGIN=EC_USER
HES_SERVICE_RANDOMIZER=9io7eotn08jr25z70oxjsw011
HES_SERVICE_TOKEN=?

