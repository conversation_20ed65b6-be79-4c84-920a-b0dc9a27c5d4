<?php

return [

	/*
	|--------------------------------------------------------------------------
	| Repository Map
	|--------------------------------------------------------------------------
	|
	| This file is for storing the repository interfaces map
	|
	*/
	'App\Interfaces\UserRepositoryInterface'            => 'App\Repositories\UserRepository',
	'App\Interfaces\AssetRepositoryInterface'           => 'App\Repositories\AssetRepository',
	'App\Interfaces\UserDetailRepositoryInterface'      => 'App\Repositories\UserDetailRepository',
	'App\Interfaces\NotificationRepositoryInterface'    => 'App\Repositories\NotificationRepository',
	'App\Interfaces\SettingsRepositoryInterface'        => 'App\Repositories\SettingsRepository',
	'App\Interfaces\TeamRepositoryInterface'            => 'App\Repositories\TeamRepository',
	'App\Interfaces\AppointmentRepositoryInterface'     => 'App\Repositories\AppointmentRepository',
	'App\Interfaces\EvaluationRepositoryInterface'      => 'App\Repositories\EvaluationRepository',
	'App\Interfaces\RaceRepositoryInterface'            => 'App\Repositories\RaceRepository',
	'App\Interfaces\RaceSessionRepositoryInterface'     => 'App\Repositories\RaceSessionRepository',
	'App\Interfaces\SessionTeamRepositoryInterface'     => 'App\Repositories\SessionTeamRepository',
	'App\Interfaces\PromotionRepositoryInterface'       => 'App\Repositories\PromotionRepository',
	'App\Interfaces\PenaltyRepositoryInterface'         => 'App\Repositories\PenaltyRepository',
	'App\Interfaces\CriteriaRepositoryInterface'        => 'App\Repositories\CriteriaRepository',
	'App\Interfaces\TechnicalDesignRepositoryInterface' => 'App\Repositories\TechnicalDesignRepository',
	'App\Interfaces\StatsRepositoryInterface'           => 'App\Repositories\StatsRepository',
	'App\Interfaces\MemberRepositoryInterface'          => 'App\Repositories\MemberRepository',
	'App\Interfaces\FormRepositoryInterface'            => 'App\Repositories\FormRepository',
	'App\Interfaces\PetitionRepositoryInterface'        => 'App\Repositories\PetitionRepository',
	'App\Interfaces\CommentRepositoryInterface'         => 'App\Repositories\CommentRepository',
	'App\Interfaces\HesCodeServiceInterface'            => 'App\Helpers\HesCodeService',
];
