<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Class CreateCommentLikesTable
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CreateCommentLikesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('comment_likes', function (Blueprint $table) {
			$table->bigIncrements('id');
			$table->boolean('is_liked');
			$table->integer('comment_id');
			$table->integer('user_id');
//			$table->index(['']);
			$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comment_likes');
    }
}
