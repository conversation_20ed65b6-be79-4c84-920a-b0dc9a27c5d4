<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePetitionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('petitions', function (Blueprint $table) {
            $table->id();
            $table->integer('team_id');
            $table->string('session_name');
            $table->string('type')->nullable();
			$table->string('target')->nullable();
			$table->text('content')->nullable();
			$table->string('like')->default(0);
			$table->string('dislike')->default(0);
			$table->string('hesitant')->default(0);
			$table->integer('active')->default(1);
			$table->integer('created_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('petitions');
    }
}
