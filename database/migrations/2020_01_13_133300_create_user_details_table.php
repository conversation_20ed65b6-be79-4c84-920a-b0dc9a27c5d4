<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_details', function (Blueprint $table) {
			$table->increments('id');
			$table->unsignedInteger('user_id');
			$table->smallInteger('gender')->nullable();
			$table->date('birthday')->nullable();
			$table->string('country')->nullable();
			$table->string('timezone')->nullable();
			$table->string('description')->nullable();
			$table->string('language', 3)->nullable();
			$table->timestamps();
			$table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_details');
    }
}
