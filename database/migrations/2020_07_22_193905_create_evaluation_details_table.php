<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEvaluationDetailsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('evaluation_details', function (Blueprint $table) {
			$table->id();
			$table->integer('evaluation_id');
			$table->integer('team_id');
			$table->integer('criteria_id');
			$table->string('value')->nullable();
			$table->boolean('compatibility')->nullable();
			$table->boolean('active')->default(1);
			$table->double('penalty_id')->nullable();
			$table->integer('after_race')->nullable();
			$table->text('notes')->nullable();
			$table->integer('updated_by')->nullable();

			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('evaluation_details');
	}
}
