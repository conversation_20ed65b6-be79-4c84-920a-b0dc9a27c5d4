<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTeamsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('teams', function (Blueprint $table) {
			$table->id();
			$table->string('team_name');
			$table->string('university_name')->nullable();
			$table->string('vehicle_name')->nullable();
			$table->string('city_name')->nullable();
			$table->integer('vehicle_number')->nullable();
			$table->integer('team_member_count')->nullable();
			$table->string('team_leader')->nullable();
			$table->string('team_leader_phone')->nullable();
			$table->string('team_leader_email')->nullable();
			$table->string('consultant_name')->nullable();
			$table->string('consultant_phone')->nullable();
			$table->string('consultant_email')->nullable();
			$table->string('curator_name')->nullable();
			$table->string('curator_phone')->nullable();
			$table->string('curator_email')->nullable();
			$table->string('driver_name')->nullable();
			$table->string('driver_phone')->nullable();
			$table->string('driver_email')->nullable();
			$table->string('second_driver_name')->nullable();
			$table->string('second_driver_phone')->nullable();
			$table->string('second_driver_email')->nullable();
			$table->string('vehicle_category')->nullable();
			$table->boolean('completed')->default(false);
			$table->integer('logo_id')->nullable();
			$table->boolean('ended')->default(false);

			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('teams');
	}
}
