<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMembersTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('members', function (Blueprint $table) {
			$table->id();
			$table->string('first_name');
			$table->string('last_name');
			$table->string('email', 190)->nullable();
			$table->integer('team_id');
			$table->string('role_in_team',32);
			$table->string('identity_number', 190)->nullable();
			$table->string('phone_number', 14)->nullable();
			$table->date('birthday')->nullable();
			$table->string('gender', 7)->nullable();
			$table->boolean('in_area')->nullable();
			$table->string('parent_name', 64)->nullable();
			$table->string('parent_phone', 14)->nullable();
			$table->string('uniform_size', 8)->nullable();
			$table->string('hes_code', 16)->nullable();
			$table->timestamps();
			$table->softDeletes();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('members');
	}
}
