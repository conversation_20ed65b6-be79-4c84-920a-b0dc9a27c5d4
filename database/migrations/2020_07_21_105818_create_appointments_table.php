<?php

use App\Enums\AppointmentStatusType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAppointmentsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('appointments', function (Blueprint $table) {
			$table->id();
			$table->integer('team_id')->unsigned()->nullable();
			$table->datetime('start_time')->nullable();
			$table->enum('status', AppointmentStatusType::getValues())->default(AppointmentStatusType::ACTIVE);
			$table->text('comments')->nullable();
			$table->integer('created_by')->default(0);
			$table->timestamps();
			$table->softDeletes();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('appointments');
	}
}
