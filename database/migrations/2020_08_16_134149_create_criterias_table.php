<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCriteriasTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('criterias', function (Blueprint $table) {
			$table->id();
			$table->string('category');
			$table->string('sub_category');
			$table->string('no');
			$table->string('subject');
			$table->integer('type')->nullable();
			$table->string('content',512);
			$table->string('raw_rule')->nullable();
			$table->string('rule')->nullable();
			$table->boolean('is_required');
			$table->boolean("allow_value");
			$table->string('allow_disqualified');
			$table->string('allow_defective');
			$table->string('allow_approve');
			$table->string('active')->default(1);
			$table->boolean('after_race')->default(false);
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('criterias');
	}
}
