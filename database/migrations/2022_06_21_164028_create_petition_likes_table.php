<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePetitionLikesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
		Schema::create('petition_likes', function (Blueprint $table) {
			$table->bigIncrements('id');
			$table->boolean('is_liked');
			$table->integer('petition_id');
			$table->integer('user_id');
//			$table->index(['']);
			$table->timestamps();
		});
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('petition_likes');
    }
}
