<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeQrCodeDefaultFromMembersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('members', function (Blueprint $table) {
			if (Schema::hasColumn('members', 'qr_code')) {
				$table->string('qr_code',190)->nullable()->change();
			}
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('members', function (Blueprint $table) {
			if (Schema::hasColumn('members', 'qr_code')) {
				$table->string('qr_code',190)->nullable(false)->change();
			}
        });
    }
}
