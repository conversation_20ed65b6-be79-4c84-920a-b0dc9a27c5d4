<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Class CreateUserAssetsTable
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CreateUserAssetsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('user_assets', function (Blueprint $table) {
			$table->increments('id');
			$table->unsignedInteger('asset_id');
			$table->string('type')->nullable();
			$table->morphs('mediable');
			$table->index(['mediable_id', 'mediable_type']);
			$table->timestamps();
			$table->softDeletes();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('user_assets');
	}
}
