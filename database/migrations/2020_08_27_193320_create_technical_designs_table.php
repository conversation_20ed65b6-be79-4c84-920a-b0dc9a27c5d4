<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTechnicalDesignsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('technical_designs', function (Blueprint $table) {
			$table->id();
			$table->integer('team_id')->nullable();
			$table->integer('team_no')->nullable();
			$table->string('key')->nullable();
			$table->integer('point')->nullable();
			$table->integer('original_point')->nullable();
			$table->boolean('copy')->nullable();
			$table->string('domestic')->nullable();
			$table->string('comment',1024)->nullable();


//			$table->text('kriter_aciklama')->nullable();
//			$table->text('geri_bildirim')->nullable();

			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('technical_designs');
	}
}
