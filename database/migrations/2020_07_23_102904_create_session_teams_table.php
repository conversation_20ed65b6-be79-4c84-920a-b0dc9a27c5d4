<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSessionTeamsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('session_teams', function (Blueprint $table) {
			$table->id();
			$table->integer('team_id');
			$table->integer('session_id');
			$table->string('start_point')->nullable();
			$table->integer('laps')->default(0);
			$table->integer('valid_laps')->default(0);
			$table->time('race_time')->nullable();

			$table->double('initial_energy_cons')->default(0);
			$table->double('last_energy_cons')->default(0);
			$table->double('total_cons')->default(0);

			$table->double('initial_hydrogen_cons')->default(0);
			$table->double('last_hydrogen_cons')->default(0);
			$table->double('total_hydrogen_cons')->default(0);

			$table->enum('status', \App\Enums\SessionTeamStatus::getValues())->nullable();
//			$table->integer('penalty')->default(0);
			$table->integer('final')->nullable();
			$table->double('score')->nullable();
			$table->boolean('best')->nullable();

			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('session_teams');
	}
}
