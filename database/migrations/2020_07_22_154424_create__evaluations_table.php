<?php

use App\Enums\EvaluationStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEvaluationsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('evaluations', function (Blueprint $table) {
			$table->id();
			$table->integer('team_id')->unsigned()->nullable();
			$table->enum('status', EvaluationStatus::getValues())
				->default(EvaluationStatus::ACTIVE);
			$table->integer('progress')->default(0);
			$table->integer('remaining')->default(0);
			$table->date('eval_date');
			$table->integer('eval_day')->default(1);
			$table->integer('after_race_started')->nullable();
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('evaluations');
	}
}
