<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeContitionFromEvaluationDetailsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::table('evaluation_details', function (Blueprint $table) {
			if (Schema::hasColumn('evaluation_details', 'condition')) {
				$table->string('condition', 500)->change();
			}
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::table('evaluation_details', function (Blueprint $table) {
			if (Schema::hasColumn('evaluation_details', 'condition')) {
				$table->string('condition')->change();
			}
		});
	}
}
