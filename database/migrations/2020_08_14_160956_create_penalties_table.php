<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePenaltiesTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('penalties', function (Blueprint $table) {
			$table->id();
			$table->integer('team_id');
			$table->integer('session_id')->default(0)->nullable();
			$table->string('subject')->nullable();
			$table->string('violation', 512)->nullable();
			$table->double('penalty');
			$table->enum('type', \App\Enums\PenaltyType::getValues());
			$table->string('conclusion')->nullable();
			$table->timestamps();
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('penalties');
	}
}
