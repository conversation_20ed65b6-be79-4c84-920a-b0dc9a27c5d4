<?php
namespace Database\Seeders;

use App\Models\Team;
use Illuminate\Database\Seeder;

/**
 * Class TeamsTableSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class TeamsTableSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		$file = storage_path('app/teams.xlsx');
		if (!file_exists($file)) {
			return;
		}
		$col = Excel::toCollection(new Team, $file);

		app(\App\Interfaces\TeamRepositoryInterface::class)
			->import($col);

		$race    = app(\App\Interfaces\RaceRepositoryInterface::class)
			->create(array(
				'name'        => 'first race',
				'description' => 'first race',
				'start_date'  => '2020-07-23',
			));
		$session = app(\App\Interfaces\RaceSessionRepositoryInterface::class)
			->create(array(
				'name'        => 'first session',
				'race_id'     => $race->id,
				'description' => 'first race',
				'start_time'  => '2020-07-23 12:00',
			));
//
//		$raceTeams = app(\App\Interfaces\RaceSessionRepositoryInterface::class)
//			->setMainModel($session)
//			->addRaceTeam([1, 2, 3, 4, 5]);
//
//		app(\App\Interfaces\SessionTeamRepositoryInterface::class)
//			->findMainModelOrFail(1)
//			->update([
//				'start_point' => 1
//			]);

		app(\App\Interfaces\TeamRepositoryInterface::class)->approveAllPossibleTeams();
	}
}
