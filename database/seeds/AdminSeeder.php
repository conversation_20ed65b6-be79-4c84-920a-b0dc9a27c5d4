<?php
namespace Database\Seeders;

use App\Interfaces\UserRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

/**
 * Class AdminSeeder
 *
 * <AUTHOR> A<PERSON><PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AdminSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		$repo  = app(UserRepositoryInterface::class);
		$admin = $repo->getUserByEmail('admin@localhost');
		$data  = [
			'first_name'        => 'Super',
			'last_name'         => 'Admin',
			'email'             => 'admin@localhost',
			'username'          => 'admin',
			'password'          => 'Rast123?.',
			'email_verified_at' => Carbon::now()->toDateTimeString(),
//			'picture_id'        => 1,
			'role'              => [
				"user",
				"superadmin"
			],
		];
		if (!$admin) {
			$repo->createUser($data);
		} else {
			$repo->setMainModel($admin)->updateUser($data);
		}
//		Bouncer::assign(Roles::SUPERADMIN)->to($admin);
	}
}
