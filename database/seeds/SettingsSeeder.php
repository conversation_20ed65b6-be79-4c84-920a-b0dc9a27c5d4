<?php
namespace Database\Seeders;

use App\Interfaces\SettingsRepositoryInterface;
use Illuminate\Database\Seeder;

/**
 * Class SettingsSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class SettingsSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		app(SettingsRepositoryInterface::class)
			->create([
				"appointment_time"       => 10,
				"appointment_slot_limit" => 3,
				"max_domestic_penalty"   => 30,
				'promotions'             => [
					"T-shirt",
					"Şap<PERSON>",
					"Çanta",
					"Ser<PERSON>fi<PERSON>",
					"Araç etiketleri",
					"Organizasyon kılavuzu",
					"Yaka kartı",
				],
			]);
	}
}

