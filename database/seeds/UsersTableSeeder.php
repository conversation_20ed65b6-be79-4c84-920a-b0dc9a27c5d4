<?php
namespace Database\Seeders;

use App\Interfaces\UserRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

/**
 * Class UsersTableSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UsersTableSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
//		DB::table('assets')->insert([
//			'type'      => \App\Enums\AssetType::IMAGE,
//			'user_id'   => 1,
//			'path'      => 'assets/2019/08/26/3524942cd12533e0d7f353edcefab187.jpg',
//			'thumbnail' => 'assets/2019/08/26/3524942cd12533e0d7f353edcefab187.jpg',
//		]);
		$users = json_decode('{
    "data": [
        {
            "id": 2,
            "first_name": "<PERSON>ç. Dr<PERSON>",
            "last_name": "<PERSON><PERSON><PERSON><PERSON>",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Motor_Driver",
                "DDK_Domestic_Parts",
                "ddk"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 3,
            "first_name": "Dr. Öğr. Üyesi Muhammet Tahir",
            "last_name": "GÜNEŞER",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-09-01T09:19:43.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Battery"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 4,
            "first_name": "Doç. Dr. Murat Erhan",
            "last_name": "BALCI",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Motor_Driver"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 5,
            "first_name": "Doç. Dr. Murat",
            "last_name": "LÜY",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Electrical_Safety",
                "DDK_Telemetry",
                "DDK_Sticker"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 6,
            "first_name": "Prof. Dr. Hasan Şakir",
            "last_name": "BİLGE",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Telemetry",
                "DDK_Sticker"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 7,
            "first_name": "Doç. Dr. Rezan DEMİR",
            "last_name": "ÇAKAN",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Battery"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 8,
            "first_name": "Prof. Dr. Mehmet Kadri",
            "last_name": "AYDINOL",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Battery"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 9,
            "first_name": "Dr. Tolgahan",
            "last_name": "KAYA",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:20.000000Z",
            "updated_at": "2020-08-29T12:09:20.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Physical_Specification",
                "DDK_Hardware",
                "DDK_Safety_Hardware"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 10,
            "first_name": "Dr. Öncü",
            "last_name": "ARARAT",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:20.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-08-29T12:09:21.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Test"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 11,
            "first_name": "Prof. Dr. Mehmet İhsan",
            "last_name": "KARAMANGİL",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-09-01T08:58:51.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Physical_Specification",
                "DDK_Test"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 12,
            "first_name": "Prof. Dr. Can Özgür",
            "last_name": "ÇOLPAN",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-09-01T08:14:47.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Hydromobile"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 13,
            "first_name": "Prof. Dr Mustafa",
            "last_name": "Şekkeli",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-09-01T08:39:17.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 14,
            "first_name": "Mustafa",
            "last_name": "Deniz",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-08-29T12:09:21.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 15,
            "first_name": "Dr. Emin",
            "last_name": "Okumuş",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-08-29T12:09:21.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Hydromobile"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 16,
            "first_name": "Orhan",
            "last_name": "KÜTÜK",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-08-29T12:09:21.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 17,
            "first_name": "Fatih",
            "last_name": "Mutluel",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-08-29T12:09:21.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Physical_Specification",
                "DDK_Hardware",
                "DDK_Safety_Hardware"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 18,
            "first_name": "Ahmet",
            "last_name": "Yaylı",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": "2020-08-29T12:09:21.000000Z",
            "created_at": "2020-08-29T12:09:21.000000Z",
            "updated_at": "2020-08-29T12:09:21.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts",
                "DDK_Physical_Specification",
                "DDK_Hardware",
                "DDK_Safety_Hardware"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 19,
            "first_name": "Randevu",
            "last_name": "Hesabi",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": null,
            "created_at": "2020-08-31T11:41:29.000000Z",
            "updated_at": "2020-08-31T11:41:29.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "DDK_Domestic_Parts"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 20,
            "first_name": "Admin",
            "last_name": "Hesabı",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": null,
            "created_at": "2020-08-31T11:43:16.000000Z",
            "updated_at": "2020-08-31T11:43:16.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "superadmin"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 21,
            "first_name": "Ebru",
            "last_name": "Akkuş",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": null,
            "created_at": "2020-08-31T11:47:22.000000Z",
            "updated_at": "2020-08-31T12:17:17.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "team",
                "appointment"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        },
        {
            "id": 22,
            "first_name": "Zeynep",
            "last_name": "Göğüsgeren",
            "email": "<EMAIL>",
            "username": null,
            "email_verified_at": null,
            "created_at": "2020-08-31T11:47:26.000000Z",
            "updated_at": "2020-08-31T11:47:26.000000Z",
            "deleted_at": null,
            "picture_url": null,
            "role": [
                "team",
                "appointment"
            ],
            "profile_completed": true,
            "details": {
                "gender": null,
                "birthday": null,
                "country": null,
                "timezone": null,
                "description": null,
                "language": null,
                "age": null
            }
        }
    ]
}', true)['data'];

		$pass = [
			'admin@localhost'              => 'Pass123?.',
			'<EMAIL>'            => '123456?123***',
			'<EMAIL>'     => 'Sifre?345***',
			'<EMAIL>' => 'YeniS?123***',
			'<EMAIL>'   => '31onur32',
			'<EMAIL>'   => 'Sfr?123***',
			'<EMAIL>'      => 'YeniSifre?123***',
			'<EMAIL>'           => 'tl?123?***',
			'<EMAIL>'            => 'Sifre**123?**',
			'<EMAIL>'       => 'yeniSifre?123?**',
			'<EMAIL>'            => 'siteSifre123?***',
			'<EMAIL>'      => '123456789',
			'<EMAIL>' => 'pass?123**?**',
			'<EMAIL>'   => 'test?12**3?',
			'<EMAIL>'          => '123456789',
			'<EMAIL>'   => 'YeniH?123Sifre***',
			'<EMAIL>' => 'Jvk7s5QjGEzsPYbm',
			'<EMAIL>'   => '5qWKKRB9xwr5h6aL',
			'<EMAIL>'       => 'randevu1234?',
			'<EMAIL>'              => 'Sifre?1234**',
			'<EMAIL>'             => 'Sifre234****',
			'<EMAIL>'           => 'Sifre234****',

		];

		$repo = app(UserRepositoryInterface::class);

		foreach ($users as $user) {
			$repo->createUser([
				'first_name'        => $user['first_name'],
				'last_name'         => $user['last_name'],
				'email'             => $user['email'],
				'username'          => $user['username'],
				'password'          => $pass[$user['email']],
				'email_verified_at' => Carbon::now()->toDateTimeString(),
//				'picture_id'        => 1
				'role'              => $user['role']
			]);
		}
	}
}
