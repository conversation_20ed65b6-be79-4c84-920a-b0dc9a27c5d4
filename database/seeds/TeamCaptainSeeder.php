<?php
namespace Database\Seeders;

use App\Enums\Roles;
use App\Models\Evaluation;
use Illuminate\Database\Seeder;

/**
 * Class BouncerSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class TeamCaptainSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		Bouncer::allow(Roles::TEAM_CAPTAIN)->toManage(\App\Models\Member::class);
	}
}
