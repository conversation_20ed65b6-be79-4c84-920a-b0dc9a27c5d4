<?php

use Illuminate\Database\Seeder;

/**
 * Class CriteriasHyromobileSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CriteriasHyromobileSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		$repo = app(\App\Interfaces\CriteriaRepositoryInterface::class);
		$seed = array(

			array(
				'category' => 'Hydromobile',
				'subject'  => 'Fuel cell',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'An output power of 300 W - 3 kW',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Pressure safety valve',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'At least 1 valve, enough to evacuate all gases, comply with the rules',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Gas flow safety valve (Flame trap or check valve)',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Follows the output of the metal hydride hydrogen cylinders',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Thermocouple',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'On the surface of the metal hydride cylinders, comply with the rules',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Flasher',
				'type'     => 2,
				'rules'    =>
					array(
						array(
							'content'    => 'A minimum diameter of 4 cm',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'A minimum height of 5 cm',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Red and rotating with a reflector',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Temperature indicator',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Electrically connected to flasher',
								'isRequired' => 1,
								'allowValue' => 1,
							),
						array(
							'content'    => 'Alert when the temperature is 10 °C above operating temperature',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),

			array(
				'category' => 'Hydromobile',
				'subject'  => 'Metal hydride cylinders',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Outside the cockpit',
								'isRequired' => 1,
								'allowValue' => 1,
							),
						array(
							'content'    => 'With a protective shield',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'With resistant belts or clamps',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Max 15 bar',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Placed properly',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Hydrogen line',
				'type'     => 2,
				'rules'    =>
					array(
						array(
							'content'    => 'Not passing through the cockpit',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Proper valves, fittings and tubing used',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Appropriate design, comply with the rules',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),

			array(
				'category' => 'Hydromobile',
				'subject'  => 'Solenoid valve',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'On the hydrogen cylinders-fuel battery line, comply with the rules',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Regulator',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'On the hydrogen cylinders-fuel battery line, comply with the rules',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hydromobile',
				'subject'  => 'Hydrogen sensors',
				'type'     => 2,
				'rules'    =>
					array(
						array(
							'content'    => 'The location where the fuel cell is located',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Alert in the event of 2% hydrogen presence in volume',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Emit an audible alarm of 80 dB to be heard at 2 meter',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Shut down the solenoid valve',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
		);

		foreach ($seed as $index => $row) {
			$repo->create($row);
		}
	}
}
