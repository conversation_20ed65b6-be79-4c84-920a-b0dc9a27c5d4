<?php

use Illuminate\Database\Seeder;

/**
 * Class CriteriasSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CriteriasSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		$repo = app(\App\Interfaces\CriteriaRepositoryInterface::class);
		$seed = array(
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Vehicle height',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'At least 1 m above ground level',
								'isRequired' => true,
								'allowValue' => true,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Minimum height',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Height of the vehicle from the ground must be a minimum of 10 cm',
								'isRequired' => true,
								'allowValue' => true,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Vehicle measurements',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Should fit within the lines drawn at the technical inspection',
								'isRequired' => true,
								'allowValue' => false,
							),
						1 =>
							array(
								'content'    => 'Technical drawing should be provided with a seperate page',
								'isRequired' => true,
								'allowValue' => false,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Cockpit (for driver and passenger)',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'A minimum height of 85 cm and width of 65 cm will be checked by emergency evacuation performance and visually',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Vehicle body',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Hood for access to other components',
								'isRequired' => true,
								'allowValue' => false,
							),
						1 =>
							array(
								'content'    => 'From the top view: no open regions, the wheels inside the body',
								'isRequired' => true,
								'allowValue' => false,
							),
						2 =>
							array(
								'content'    => 'Fragile windows / sharp ends / protruding edges etc.',
								'isRequired' => true,
								'allowValue' => false,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Door',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '50 cm × 80 cm frame can pass through the door',
								'isRequired' => true,
								'allowValue' => false,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Door mechanism',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Fixed to the body with a safe connecting element',
								'isRequired' => true,
								'allowValue' => false,
							),
						1 =>
							array(
								'content'    => 'Door mechanism can be closed without any manual intervention',
								'isRequired' => true,
								'allowValue' => false,
							),
						2 =>
							array(
								'content'    => 'Can be opened from outside/no possibility of unintended opening',
								'isRequired' => true,
								'allowValue' => false,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Tire',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'ANLAS 90X90 16',
								'isRequired' => true,
								'allowValue' => true,
							),
					),
			),
			array(
				'category' => 'Physical Specification',
				'subject'  => 'Flag',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'A minimum of 20 × 30 cm',
								'isRequired' => true,
								'allowValue' => false,
							),
					),
			),
////////////////// Hardware
			array(
				'category' => 'Hardware',
				'subject'  => 'Windscreen',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Transparent windows that do not shatter during collisions',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hardware',
				'subject'  => 'Wiper',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Working properly',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hardware',
				'subject'  => 'Rearview mirrors',
				'type'     => null,
				'rules'    =>
					array(
						array(
							'content'    => 'On both sides of the cockpit',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'with a minimum reflection area of 50 cm2',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Driver can see the text shown',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
			array(
				'category' => 'Hardware',
				'subject'  => 'Horn',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Able to sound 3 second continuously at a sound level of 80 dB',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Hardware',
				'subject'  => 'Headlights',
				'type'     => null,
				'rules'    =>
					array(
						array(
							'content'    => '2 headlights',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Seen from a distance of 25 m',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
///////
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Fire extinguishers',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '1x2 kg or 2x1 kg',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Roll bars Roll cages',
				'type'     => null,
				'rules'    =>
					array(
						array(
							'content'    => 'One roll bar in the front and one roll bar at the rear',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'A minimum yield strength of 200 MPa',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Roll bar perpendicular to the bottom of the vehicle',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'The front roll bar starts at least 3 cm above the steering wheel',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'The rear roll bar starts at least 5 cm above the helmet',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Closed and rolled pipe or box profiles',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Independent from the chassis',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Welding/bolts used according to specified rules.',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
///////////////////////////
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Tow bars',
				'type'     => null,
				'rules'    =>
					array(
						array(
							'content'    => 'One in the front and one at the rear',
							'isRequired' => 1,
							'allowValue' => 1,
						),

						array(
							'content'    => 'With a minimum internal diameter of 20 mm, made of steel',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Steering wheel',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Closed form',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Safety Belts',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Fixed at four or five points, compatible with FIA standards',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Driver outfit and equipment',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Compatible with FIA standards',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Safety Hardware',
				'subject'  => 'Seat',
				'type'     => null,
				'rules'    =>
					array(
						array(
							'content'    => 'Driver seat is compatible with FIA standards, fixed to the chassis',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Passenger seat is compatible with FIA standards, fixed to the chassis',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Makes an angle of at most 30 degrees with the normal axis of the chassis',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
/////////////////
			array(
				'category' => 'Electrical Safety',
				'subject'  => 'Electrical cable connections',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'No bare cable/proper insulation',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Electrical Safety',
				'subject'  => 'Overcurrent breaker',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'In the power conditioner circuit, on main power line, and with proper rated values',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Electrical Safety',
				'subject'  => 'Joule meter connection',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'No extra battery apart from the main battery package',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
//////////
			array(
				'category' => 'Battery',
				'subject'  => 'Battery type',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Li-Ion, Li-Polimer',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Battery',
				'subject'  => 'Battery management system',
				'type'     => null,
				'rules'    =>
					array(
						array(
							'content'    => 'Electrical measurement on display',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Temperature test for battery cell',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Temperature sensors',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),
			array(
				'category' => 'Battery',
				'subject'  => 'Battery temperature measurement',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Flasher, buzzer and temperature indicator',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Battery',
				'subject'  => 'Battery datasheet',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'All the data should be available',
								'isRequired' => 1,
								'allowValue' => 1,
							),
						array(
							'content'    => 'Must be presented',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),

			array(
				'category' => 'Battery',
				'subject'  => 'Battery box',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Proper material',
								'isRequired' => 1,
								'allowValue' => 1,
							),
						array(
							'content'    => 'Hood for access to battery package',
							'isRequired' => 1,
							'allowValue' => 1,
						),
						array(
							'content'    => 'Proper design',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),

			array(
				'category' => 'Battery',
				'subject'  => 'Fixing battery box',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Fixed properly with bolts and nuts, grade 8.8 and a min. diameter of 8 mm',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Battery',
				'subject'  => 'Safety wall',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Proper material',
								'isRequired' => 0,
								'allowValue' => 0,
							),
					),
			),
/////////////////
			array(
				'category' => 'Motor Driver',
				'subject'  => 'Motor driver box',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'The motor driver circuit must be covered with a box',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Motor Driver',
				'subject'  => 'Plug-in connectors',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Plug-in connectors must be used for connections between the motor driver circuit and other units such as motor, battery, etc.',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
//////////////
			array(
				'category' => 'Telemetry',
				'subject'  => 'Vehicle speed',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Vehicle speed',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Telemetry',
				'subject'  => 'Temperatures and voltages of the battery cells',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Temperatures and voltages of the battery cells',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Telemetry',
				'subject'  => 'Motor temperatures and voltages',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Motor temperatures and voltages',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Telemetry',
				'subject'  => 'Amount of energy remaining',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Amount of energy remaining',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
//////////////////////

			array(
				'category' => 'Test',
				'subject'  => 'Brake lights',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'At least one brake light',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Test',
				'subject'  => 'Brake test',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Seen from a distance of 25 m',
								'isRequired' => 1,
								'allowValue' => 1,
							),
						array(
							'content'    => 'Functional',
							'isRequired' => 1,
							'allowValue' => 1,
						),
					),
			),

			array(
				'category' => 'Test',
				'subject'  => 'Steering',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Functional',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Test',
				'subject'  => 'Brake system',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Seperate circuit hydraulic',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Test',
				'subject'  => 'Emergency Evacuation  (driver and reserve driver)',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'No longer than 10 seconds, without help',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),

			array(
				'category' => 'Test',
				'subject'  => 'Vehicle dynamic testing',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Time',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Test',
				'subject'  => 'Speedometer',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Functional',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
//////////////////////
			array(
				'category' => 'Sticker',
				'subject'  => 'Emergency stop button',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Two buttons, one inside and one outside the vehicle',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),

		);

		foreach ($seed as $index => $row) {
			$repo->create($row);
		}
	}
}
