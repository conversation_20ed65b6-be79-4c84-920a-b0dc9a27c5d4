<?php

use Illuminate\Database\Seeder;

/**
 * Class CriteriasDomesticSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CriteriasDomesticSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		$repo = app(\App\Interfaces\CriteriaRepositoryInterface::class);
		$seed = array(
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'mandatory sub-components',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '1. Motor',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'mandatory sub-components',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '2. Motor driver',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'mandatory sub-components',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '3. BMS',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'mandatory sub-components',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '4. EMS',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '5. Emb. Rech Unit',
								'isRequired' => 1,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '6. Battery Packaging',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '7. Elec. Dif. App.',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '8. VCU',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '9. Fuel Cell',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => 2,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '10. Fuel Cell Cont. Sys.',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '11. Insulation monitoring device',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '12. Steering system',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Optional sub-component',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => '13. Door mechanism',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
			array(
				'category' => 'Domestic Parts',
				'subject'  => 'Other report notes',
				'type'     => null,
				'rules'    =>
					array(
						0 =>
							array(
								'content'    => 'Original Design',
								'isRequired' => 0,
								'allowValue' => 1,
							),
					),
			),
		);

		foreach ($seed as $index => $row) {
			$repo->create($row);
		}
	}
}
