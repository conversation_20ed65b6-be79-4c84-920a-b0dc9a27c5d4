<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;

/**
 * Class FilesSeeder
 *
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class FilesSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		$repo  = app(\App\Interfaces\AssetRepositoryInterface::class);
		$path  = '';
		$asset = $repo->create([
			"path"    => $path,
//			"thumbnail" => $thumbnail,
			"type"    => \App\Enums\AssetType::FILE,
			"user_id" => 1,
//			"size"      => number_format($file / (1024 * 1024), 3)
		]);
		$repo->addSystemFile($asset->id, 'A file');
	}
}
