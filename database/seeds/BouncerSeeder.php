<?php
namespace Database\Seeders;

use App\Enums\Roles;
use App\Models\Evaluation;
use App\Models\Petition;
use App\Models\User;
use Bouncer;
use Illuminate\Database\Seeder;
use Takdeniz\LikableComment\Models\Comment;

/**
 * Class BouncerSeeder
 *
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class BouncerSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
	{
		Bouncer::allow(Roles::SUPERADMIN)->everything();
//		Bouncer::allow(Roles::USER)->toManage(\App\Models\Team::class);

		// ##### DDK
		Bouncer::allow(Roles::DDK)
			->to([\App\Enums\Abilities::LIST, \App\Enums\Abilities::VIEW], '*');

		Bouncer::allow(Roles::DDK)->to([
			\App\Enums\EvaluationAbilities::LIST,
			\App\Enums\EvaluationAbilities::CREATE,
			\App\Enums\EvaluationAbilities::DELETE,
			\App\Enums\EvaluationAbilities::VIEW,
			\App\Enums\EvaluationAbilities::OPEN,
		], Evaluation::class);

		Bouncer::allow(Roles::DDK)->toManage(\App\Models\TechnicalDesign::class);
		Bouncer::allow(Roles::DDK)->to([
			\App\Enums\EvaluationAbilities::LIKE,
			\App\Enums\EvaluationAbilities::COMMENT,
		], Petition::class);
		Bouncer::allow(Roles::DDK)->to([
			\App\Enums\EvaluationAbilities::DELETE,
		], Comment::class);

		Bouncer::allow(Roles::TEAM)->toManage([
			\App\Models\Team::class,
			\App\Models\Member::class,
		]);

		Bouncer::allow(Roles::HES_CODE)->toManage(\App\Models\HesCode::class);

		// @hbt1903
		// Team captain abilities are defined (will be modified if required)
		// TODO: check if something is missing
		Bouncer::allow(Roles::TEAM_CAPTAIN)
			->toOwn(\App\Models\Team::class)->to([
				\App\Enums\Abilities::UPDATE,
				\App\Enums\EvaluationAbilities::VIEW,
			]);

//		Bouncer::allow(Roles::TEAM_CAPTAIN)->toOwn(\App\Models\Appointment::class)->to([
//			\App\Enums\Abilities::UPDATE,
//		]);

		Bouncer::allow(Roles::TEAM_CAPTAIN)->to([
			\App\Enums\Abilities::LIST,
			\App\Enums\Abilities::VIEW,
//			\App\Enums\Abilities::CREATE
		], \App\Models\Appointment::class);

		Bouncer::allow(Roles::TEAM_CAPTAIN)->toManage(\App\Models\Member::class);
		Bouncer::allow(Roles::TEAM_CAPTAIN)->toManage(\App\Models\Form::class);
		// @hbt1903
		// Team captain role ends

		Bouncer::allow(Roles::APPOINTMENT)->toManage(\App\Models\Appointment::class);
		Bouncer::allow(Roles::RACE)->toManage(\App\Models\Race::class);
		Bouncer::allow(Roles::DDK_ALL)->toManage(Evaluation::class);

		Bouncer::allow(Roles::GIVE_STICKER)
			->to([\App\Enums\EvaluationAbilities::STICKER], Evaluation::class);

		Bouncer::allow(Roles::CLOSE_EVALUATION)
			->to([
				\App\Enums\EvaluationAbilities::OPEN,
				\App\Enums\EvaluationAbilities::CLOSE_EVALUATION,
			], Evaluation::class);

#		### GUIDE
		Bouncer::allow(Roles::GUIDE)
			->to([\App\Enums\Abilities::LIST, \App\Enums\Abilities::VIEW], '*');

		Bouncer::allow(Roles::GUIDE)->toManage(\App\Models\Evaluation::class);
		Bouncer::allow(Roles::GUIDE)->toManage(\App\Models\Member::class);
		Bouncer::allow(Roles::GUIDE)->toManage(\App\Models\Appointment::class);
		Bouncer::allow(Roles::GUIDE)->toManage(\App\Models\Team::class);
		Bouncer::allow(Roles::GUIDE)->toManage(\App\Models\Promotion::class);
		Bouncer::allow(Roles::GUIDE)->to([
			\App\Enums\EvaluationAbilities::CREATE,
			\App\Enums\EvaluationAbilities::DELETE,
		], \App\Models\Petition::class);

		User::find(1) && Bouncer::assign(Roles::SUPERADMIN)->to(User::find(1));
	}
}
