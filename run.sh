#!/usr/bin/env bash
#echo > storage/app/public/deploy.log
#echo > storage/app/public/deploy-fe.log

#echo 'SENT_REGISTER_MAIL=true' >> .env
#echo 'JWT_TTL=60000' >> .env
#php composer.phar install --ignore-platform-reqs


#mysqldump -u bito -pB79Yahk7H3B+5 --no-tablespaces efficiency > storage/app/public/temp_44tr.dat
#mysqldump -u bito -pB79Yahk7H3B+5 --no-tablespaces lise_efficiency > storage/app/public/temp_44tr_lise.dat


#sleep 3
#cd ../efficiency-challange
#git checkout -- package-lock.json

#tail -n 400 storage/logs/laravel.log

#php artisan import:team --limit 50 --page 10 --offset 1600


#sleep $(( RANDOM % 11));

#cat ec_inter_members.sql | mysql -u bito -pB §79Yahk7H3B+5 efficiency
#cat ec_lise_members.sql | mysql -u bito -pB79Yahk7H3B+5 lise_efficiency

#cat sql_changes.sql | mysql -u bito -pB79Yahk7H3B+5 efficiency
#cat sql_changes.sql | mysql -u bito -pB79Yahk7H3B+5 lise_efficiency


#php artisan roles:clear
#php artisan db:seed --class=BouncerSeeder
#php artisan criteria:roles

#php artisan tech:fix
echo 'end'
