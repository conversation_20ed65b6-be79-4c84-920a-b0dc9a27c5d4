# Efficiency Challenge


### Installation

öncelikle sunucuda bir ssh public key bizimle paylaşılmalı, bu keyi git repositoyrisine ekleyecegiz

Sunucuda serve ilgili dizinde asağıdaki komutlar uygulanmalı.

- `<NAME_EMAIL>:takdeniz/efficiency-challenge.git`
- `cp .env.example .env`
#### daha sonra .env dosyası içerisindeki yapılandırma ayarları yapılmalı
- DB_ öneki ile başlayan kısımlar veritabanı bilgileriyle doldurulmalı
- MAIL_ ile başlayan kısımlar ise mail sunucusu bilgileriyle doldurulmalı

#### yapılandırma ayarlaarı tamamlandıktan sonra altaki komutlar uygulanmalıdır
- `composer install` yada 
- `php vendor/composer/composer/bin/composer install`
- `php artisan jwt:secret`
- `php artisan migrate --seed`
- `php artisan storage:link`

###
- criterler import edilmeli - {{base_url}}/admin/import-criteria
- 


local development için
- `php artisan serve`


ardından web sunucu başlatılmalı (nginx, apache vb.)


yum install gd gd-devel php-gd
yum install php-xml
yum install php-mbstring
yum install php-pecl-zip
sudo yum install php73-php-pdo php73-php-mysqlnd


###new updates
- add hes service conf .env


## server conf

update in php.ini
upload_max_filesize = 100M;
post_max_size = 100M;
memory_limit = 256M;

php artisan roles:clear
php artisan db:seed --class=AdminSeeder
php artisan db:seed --class=BouncerSeeder
php artisan criteria:roles   

php artisan migrate
php artisan tech:fix

