<?php

return [

	/*
	|--------------------------------------------------------------------------
	| Do<PERSON><PERSON><PERSON><PERSON> mesajları
	|--------------------------------------------------------------------------
	|
	| Aşağ<PERSON>dak<PERSON> öğeler doğrulama sınıfı tarafından kullanılan varsayılan hata
	| mesajlarını içermektedir. `size` gibi bazı kuralların birden çok çeşidi
	| bulunmaktadır. Her biri ayrı ayrı düzenlenebilir.
	|
	*/

	'accepted'             => ':attribute kabul edilmelidir.',
	'active_url'           => ':attribute geçerli bir URL olmalıdır.',
	'after'                => ':attribute değeri :date tarihinden sonra olmalıdır.',
	'after_or_equal'       => ':attribute değeri :date tarihinden sonra veya eşit olmalıdır.',
	'alpha'                => ':attribute sadece harflerden oluşmalıdır.',
	'alpha_dash'           => ':attribute sadece harfler, rakamlar ve tirelerden oluşmalıdır.',
	'alpha_num'            => ':attribute sadece harfler ve rakamlar içermelidir.',
	'array'                => ':attribute dizi olmalıdır.',
	'before'               => ':attribute değeri :date tarihinden önce olmalıdır.',
	'before_or_equal'      => ':attribute değeri :date tarihinden önce veya eşit olmalıdır.',
	'between'              => [
		'numeric' => ':attribute :min - :max arasında olmalıdır.',
		'file'    => ':attribute :min - :max arasındaki kilobayt değeri olmalıdır.',
		'string'  => ':attribute :min - :max arasında karakterden oluşmalıdır.',
		'array'   => ':attribute :min - :max arasında nesneye sahip olmalıdır.',
	],
	'boolean'              => ':attribute sadece doğru veya yanlış olmalıdır.',
	'confirmed'            => ':attribute tekrarı eşleşmiyor.',
	'date'                 => ':attribute geçerli bir tarih olmalıdır.',
	'date_equals'          => ':attribute ile :date aynı tarihler olmalıdır.',
	'date_format'          => ':attribute :format biçimi ile eşleşmiyor.',
	'different'            => ':attribute ile :other birbirinden farklı olmalıdır.',
	'digits'               => ':attribute :digits haneden oluşmalıdır.',
	'digits_between'       => ':attribute :min ile :max arasında haneden oluşmalıdır.',
	'dimensions'           => ':attribute görsel ölçüleri geçersiz.',
	'distinct'             => ':attribute alanı yinelenen bir değere sahip.',
	'email'                => ':attribute alanına girilen e-posta adresi geçersiz.',
	'exists'               => 'Seçili :attribute geçersiz.',
	'file'                 => ':attribute dosya olmalıdır.',
	'filled'               => ':attribute alanının doldurulması zorunludur.',
	'ends_with'            => ':attribute, şunlardan biriyle bitmelidir :values',
	'gt'                   => [
		'numeric' => ':attribute, :value değerinden büyük olmalı.',
		'file'    => ':attribute, :value kilobayt boyutundan büyük olmalı.',
		'string'  => ':attribute, :value karakterden uzun olmalı.',
		'array'   => ':attribute, :value taneden fazla olmalı.',
	],
	'gte'                  => [
		'numeric' => ':attribute, :value kadar veya daha fazla olmalı.',
		'file'    => ':attribute, :value kilobayt boyutu kadar veya daha büyük olmalı.',
		'string'  => ':attribute, :value karakter kadar veya daha uzun olmalı.',
		'array'   => ':attribute, :value tane veya daha fazla olmalı.',
	],
	'image'                => ':attribute alanı resim dosyası olmalıdır.',
	'in'                   => ':attribute değeri geçersiz.',
	'in_array'             => ':attribute alanı :other içinde mevcut değil.',
	'integer'              => ':attribute tamsayı olmalıdır.',
	'ip'                   => ':attribute geçerli bir IP adresi olmalıdır.',
	'ipv4'                 => ':attribute geçerli bir IPv4 adresi olmalıdır.',
	'ipv6'                 => ':attribute geçerli bir IPv6 adresi olmalıdır.',
	'json'                 => ':attribute geçerli bir JSON değişkeni olmalıdır.',
	'lt'                   => [
		'numeric' => ':attribute, :value değerinden küçük olmalı.',
		'file'    => ':attribute, :value kilobayt boyutundan küçük olmalı.',
		'string'  => ':attribute, :value karakterden kısa olmalı.',
		'array'   => ':attribute, :value taneden az olmalı.',
	],
	'lte'                  => [
		'numeric' => ':attribute, :value kadar veya daha küçük olmalı.',
		'file'    => ':attribute, :value kilobayt boyutu kadar veya daha küçük olmalı.',
		'string'  => ':attribute, :value karakter kadar veya daha kısa olmalı.',
		'array'   => ':attribute, :value tane veya daha az olmalı.',
	],
	'max'                  => [
		'numeric' => ':attribute değeri :max değerinden küçük olmalıdır.',
		'file'    => ':attribute boyutu :max kilobayt değerinden küçük olmalıdır.',
		'string'  => ':attribute değeri :max karakterden küçük olmalıdır.',
		'array'   => ':attribute değeri :max adedinden az nesneye sahip olmalıdır.',
	],
	'mimes'                => ':attribute dosya biçimi :values olmalıdır.',
	'mimetypes'            => ':attribute dosya biçimi :values olmalıdır.',
	'min'                  => [
		'numeric' => ':attribute değeri :min değerinden büyük olmalıdır.',
		'file'    => ':attribute boyutu :min kilobayt değerinden büyük olmalıdır.',
		'string'  => ':attribute değeri :min karakterden büyük olmalıdır.',
		'array'   => ':attribute en az :min nesneye sahip olmalıdır.',
	],
	'not_in'               => 'Seçili :attribute geçersiz.',
	'not_regex'            => ':attribute biçimi geçersiz.',
	'numeric'              => ':attribute sayı olmalıdır.',
	'password'             => 'Parola geçersiz.',
	'present'              => ':attribute alanı mevcut olmalıdır.',
	'regex'                => ':attribute biçimi geçersiz.',
	'required'             => ':attribute alanı gereklidir.',
	'required_if'          => ':attribute alanı, :other :value değerine sahip olduğunda zorunludur.',
	'required_unless'      => ':attribute alanı, :other alanı :value değerlerinden birine sahip olmadığında zorunludur.',
	'required_with'        => ':attribute alanı :values varken zorunludur.',
	'required_with_all'    => ':attribute alanı herhangi bir :values değeri varken zorunludur.',
	'required_without'     => ':attribute alanı :values yokken zorunludur.',
	'required_without_all' => ':attribute alanı :values değerlerinden herhangi biri yokken zorunludur.',
	'same'                 => ':attribute ile :other eşleşmelidir.',
	'size'                 => [
		'numeric' => ':attribute :size olmalıdır.',
		'file'    => ':attribute :size kilobyte olmalıdır.',
		'string'  => ':attribute :size karakter olmalıdır.',
		'array'   => ':attribute :size nesneye sahip olmalıdır.',
	],
	'starts_with'          => ':attribute şunlardan biri ile başlamalıdır: :values',
	'string'               => ':attribute dizge olmalıdır.',
	'timezone'             => ':attribute geçerli bir saat dilimi olmalıdır.',
	'unique'               => ':attribute daha önceden kayıt edilmiş.',
	'uploaded'             => ':attribute yüklemesi başarısız.',
	'url'                  => ':attribute biçimi geçersiz.',
	'uuid'                 => ':attribute bir UUID formatına uygun olmalı.',

	/*
	|--------------------------------------------------------------------------
	| Özelleştirilmiş doğrulama mesajları
	|--------------------------------------------------------------------------
	|
	| Bu alanda her niteleyici (attribute) ve kural (rule) ikilisine özel hata
	| mesajları tanımlayabilirsiniz. Bu özellik, son kullanıcıya daha gerçekçi
	| metinler göstermeniz için oldukça faydalıdır.
	|
	| Örnek olarak:
	|
	| 'email.email': 'Girdiğiniz e-posta adresi geçerli değil.'
	| 'x.regex': 'x alanı için "a-b.c" formatında veri girmelisiniz.'
	|
	*/

	'custom'             => [
		'x' => [
			'regex' => 'x alanı için "a-b.c" formatında veri girmelisiniz.',
		],
	],

	//Custom rules
	'hes_code_not_valid' => ':name adlı üyenin HES kodu geçerli değil (:status)',
	'hes_code_not_match' => ':name adlı üyenin HES kodu T.C no ile uyumsuz',

	/*
	|--------------------------------------------------------------------------
	| Özelleştirilmiş niteleyici isimleri
	|--------------------------------------------------------------------------
	|
	| Bu alandaki bilgiler "email" gibi niteleyici isimlerini "e-posta adresi"
	| gibi daha okunabilir metinlere çevirmek için kullanılır. Bu bilgiler
	| hata mesajlarının daha temiz olmasını sağlar.
	|
	| Örnek olarak:
	|
	| 'email' => 'e-posta adresi',
	| 'password' => 'parola',
	|
	*/
	'requiredMember'     => 'Takımda :attribute türünde bir üye bulunmalıdır',
	'attributes'         => [
		'first_name'      => "Adı",
		'last_name'       => "Soyadı",
		'email'           => "Email",
		'team_id'         => "Takım ID",
		'role_in_team'    => "Görevi",
		'identity_number' => "TC Kimlik NO",
		'phone_number'    => "Telefon NO",
		'birthday'        => "Doğum Tarihi",
		'gender'          => "Cinsiyet",
		'in_area'         => "Yarış Alanında Bulunma",
		'parent_name'     => "Ebeveyn Adı",
		'parent_phone'    => "Ebeveyn Tel NO",
		'uniform_size'    => "T-shirt Bedeni",
		'hes_code'        => "HES Kodu",
		'picture_id'      => "Profil Resmi",

		'file' => "Dosya",
		'team' => "Takım",

		'member'               => 'Üye',
		'captain'              => 'Kaptan',
		'driver'               => 'Sürücü',
		'reserveDriver'        => 'Yedek Sürücü',
		'academicAdvisor'      => 'Akademik Danışman',
		'lise_academicAdvisor' => 'Danışman Öğretmen',
		'curator'              => 'Sorumlu',

		'city_name'         => 'Sehir adı',
		'team_member_count' => 'Üye sayısı',
		'university_name'   => 'Üniversite adı',
		'vehicle_name'      => 'Araç adı',
		'vehicle_number'    => 'Araç numarası',

	],

];
