<?php

	use Dingo\Api\Routing\Router;
	use Illuminate\Http\Request;
	use App\Http\Controllers\Auth\LoginController;
	use App\Http\Controllers\Auth\RegisterController;
	use App\Http\Controllers\Asset\AssetController;
	use App\Http\Controllers\Asset\AttachmentController;
	use App\Http\Controllers\User\UserController;
	use App\Http\Controllers\User\UserNotificationController;
	use App\Http\Controllers\Team\TeamController;
	use App\Http\Controllers\Team\MyTeamController;
	use App\Http\Controllers\Team\MemberController;
	use App\Http\Controllers\Team\PromotionController;
	use App\Http\Controllers\Team\PenaltyController;
	use App\Http\Controllers\Team\AppointmentController;
	use App\Http\Controllers\Team\EvaluationController;
	use App\Http\Controllers\Team\TechnicalDesignController;
	use App\Http\Controllers\Team\FormController;
	use App\Http\Controllers\Team\PetitionController;
	use App\Http\Controllers\Team\CommentController;
	use App\Http\Controllers\Race\RaceController;
	use App\Http\Controllers\Race\RaceSessionController;
	use App\Http\Controllers\Race\SessionTeamController;
	use App\Http\Controllers\Stats\StatsController;
	use App\Http\Controllers\Admin\AdminUserController;
	use App\Http\Controllers\Admin\AdminSettingsController;
	use App\Http\Controllers\Admin\AdminCriteriaController;

	Route::middleware('auth:api')->get('/user', fn(Request $request) => $request->user());

	/** @var Router $api */
	$api = app(Router::class);

	$api->version('v1', function (Router $router) {
		$router->get('hello', fn() => 'hi');
		$router->get('eval-export/{id}', [EvaluationController::class, 'export']);
		$router->get('export', [TechnicalDesignController::class, 'export']);
		$router->post('import', [TechnicalDesignController::class, 'import']);
		$router->get('team-evaluation/{teamId}', [EvaluationController::class, 'showPublic']);
	});

	$api->version('v1', [], function (Router $router) {
		$router->post('login', [LoginController::class, 'login'])->name('user.login');
		$router->get('logout', [LoginController::class, 'logout'])->name('user.logout');
		$router->post('register', [RegisterController::class, 'register'])->name('user.register');
	});

	$api->version('v1', ['middleware' => 'api.auth'], function (Router $router) {
		// asset
		$router->resource('asset', AssetController::class, ['only' => ['store', 'destroy']]);
		$router->get('system-files', [AttachmentController::class, 'index']);
		$router->post('system-files', [AttachmentController::class, 'store']);
		$router->delete('system-files/{id}', [AttachmentController::class, 'destroy']);

		// user
		$router->get('user', [UserController::class, 'show']);
		$router->patch('user', [UserController::class, 'update']);
		$router->delete('user', [UserController::class, 'destroy']);

		// notification
		$router->get('user/notification', [UserNotificationController::class, 'index']);
		$router->get('user/notification/unread', [UserNotificationController::class, 'unread']);
		$router->get('user/notification/read', [UserNotificationController::class, 'read']);
		$router->put('user/notification/{id}/read', [UserNotificationController::class, 'markAsRead']);

		// team
		$router->resource('team', TeamController::class);
		$router->post('team/{id}/attach', [TeamController::class, 'attach']);
		$router->post('team-kys', [TeamController::class, 'getTeamsFromKYS']);
		$router->put('team/{id}/approve', [TeamController::class, 'approve']);
		$router->put('approve-all-teams', [TeamController::class, 'approveAllPossibleTeams']);
		$router->put('give-all-vehicle-numbers', [TeamController::class, 'giveVehicleNumberForAllTeams']);
		$router->post('team/import', [TeamController::class, 'import']);
		$router->get('team-export', [TeamController::class, 'export']);
		$router->put('team/{id}/vehicle-number', [TeamController::class, 'vehicleNumber']);
		$router->get('team/{id}/check', [TeamController::class, 'checkTeam']);

		// my team
		$router->get('my-team', [MyTeamController::class, 'show']);
		$router->patch('my-team', [MyTeamController::class, 'update']);
		$router->delete('my-team', [MyTeamController::class, 'destroy']);
		$router->get('my-team-check', [MyTeamController::class, 'checkMyTeam']);
		$router->put('my-team/vehicle-number', [MyTeamController::class, 'vehicleNumber']);

		// member
		$router->resource('member', MemberController::class);
		$router->get('member-team-export/{teamId}', [MemberController::class, 'export']);
		$router->get('member-qr-export/{teamId}', [MemberController::class, 'makeTeamQRCodePdf']);
		$router->get('member-export', [MemberController::class, 'exportAll']);
		$router->put('member/{id}/qr-code', [MemberController::class, 'makeQRCode']);
		$router->put('member-qr-code', [MemberController::class, 'makeQrCodeForAllMembers']);

		// promotion
		$router->resource('promotion', PromotionController::class);
		$router->get('promotion-export/{teamId}', [PromotionController::class, 'export']);
		$router->get('promotion-export-all', [PromotionController::class, 'exportAll']);
		$router->put('promotion', [PromotionController::class, 'multiPromotion']);

		// penalty
		$router->resource('penalty', PenaltyController::class);
		$router->get('penalty-team-export/{teamId}', [PenaltyController::class, 'export']);
		$router->get('penalty-export', [PenaltyController::class, 'exportAll']);
		$router->post('penalty-import', [PenaltyController::class, 'import']);

		// appointment
		$router->resource('appointment', AppointmentController::class);
		$router->post('appointment/auto', [AppointmentController::class, 'autoAppointment']);
		$router->put('appointment/{id}/join', [AppointmentController::class, 'join']);
		$router->post('appointment/import', [AppointmentController::class, 'import']);
		$router->get('appointment-export', [AppointmentController::class, 'export']);

		// evaluation
		$router->resource('evaluation', EvaluationController::class);
		$router->get('evaluation-stats', [EvaluationController::class, 'evalAnalytics']);
		$router->get('evaluation-stats-export', [EvaluationController::class, 'exportAnalytics']);
		$router->get('evaluation-status-export', [EvaluationController::class, 'exportEvalStatus']);
		$router->patch('evaluation-detail/{id}', [EvaluationController::class, 'updateDetail']);
		$router->put('evaluation/{id}/close', [EvaluationController::class, 'close']);
		$router->put('evaluation/{id}/reopen', [EvaluationController::class, 'reOpen']);
		$router->put('evaluation/{id}/check', [EvaluationController::class, 'checkSticker']);
		$router->put('evaluation/{id}/sticker', [EvaluationController::class, 'giveSticker']);
		$router->get('evaluation/{id}/export', [EvaluationController::class, 'export']);
		$router->put('evaluation/{id}/start-after-race', [EvaluationController::class, 'startAfterRaceChecks']);
		$router->get('evaluation-success-exports', [EvaluationController::class, 'exportSuccess']);
		$router->put('evaluation-start-after-checks', [EvaluationController::class, 'startAllSuccessAfterRaceChecks']);
		$router->put('evaluation-send-mails', [EvaluationController::class, 'sendEvaluationStatusMails']);

		// race
		$router->resource('race', RaceController::class);
		$router->put('race/{id}/end', [RaceController::class, 'endRace']);
		$router->put('race/{id}/calculate-best-score', [RaceController::class, 'calculateBestScore']);
		$router->get('race/{id}/export-scores', [RaceController::class, 'exportScores']);
		$router->get('race/{id}/export-all-scores', [RaceController::class, 'exportAllScores']);

		$router->resource('race-session', RaceSessionController::class);
		$router->post('race-session/{id}/add-team', [RaceSessionController::class, 'addRaceTeam']);
		$router->put('race-session/{id}/add-all-teams', [RaceSessionController::class, 'addAllTeams']);
		$router->get('race-session/{id}/export-session-team', [RaceSessionController::class, 'exportSessionTeams']);
		$router->resource('session-team', SessionTeamController::class);

		// technical design
		$router->resource('technical-design', TechnicalDesignController::class);
		$router->post('technical-design-import', [TechnicalDesignController::class, 'import']);
		$router->get('technical-design-export', [TechnicalDesignController::class, 'export']);
		$router->get('technical-design-team/{teamId}', [TechnicalDesignController::class, 'listByTeam']);

		// stats
		$router->get('stats', [StatsController::class, 'stats']);
		$router->get('stats-has-appointment', [StatsController::class, 'multiAppointmentTeam']);
		$router->get('stats-appointment', [StatsController::class, 'appointmentNotJoined']);

		// form & petition & comment
		$router->resource('form', FormController::class);
		$router->resource('petition', PetitionController::class);
		$router->put('petition/{id}/comment', [CommentController::class, 'comment']);
		$router->put('petition/{id}/like', [PetitionController::class, 'like']);
		$router->put('petition/{id}/dislike', [PetitionController::class, 'dislike']);
		$router->put('petition/{id}/hesitant', [PetitionController::class, 'hesitant']);
		$router->put('comment/{id}/like', [CommentController::class, 'like']);
		$router->put('comment/{id}/dislike', [CommentController::class, 'dislike']);
		$router->delete('comment/{id}', [CommentController::class, 'destroy']);
	});

	$api->version('v1', ['middleware' => 'api.auth'], function (Router $router) {
		// admin
		$router->resource('admin/user', AdminUserController::class);
		$router->get('admin/user-roles', [AdminUserController::class, 'getRoles']);
		$router->post('admin/import-criteria', [AdminCriteriaController::class, 'import']);
		$router->put('admin/criteria', [AdminCriteriaController::class, 'multiCreate']);
		$router->resource('admin/criteria', AdminCriteriaController::class);
		$router->get('admin/settings', [AdminSettingsController::class, 'index']);
		$router->post('admin/settings', [AdminSettingsController::class, 'store']);
		$router->get('admin/clear', [AdminSettingsController::class, 'clear']);
		$router->put('admin/settings/sync', [AdminSettingsController::class, 'sync']);
		$router->post('admin/import-ddk', [AdminUserController::class, 'importDDK']);
		$router->post('admin/update-ddk', [AdminUserController::class, 'updateDDK']);
		$router->post('admin/import-users', [AdminUserController::class, 'importUsers']);
		$router->get('admin/user-get-roles', [AdminUserController::class, 'listUserRoles']);
		$router->put('admin/user-put-roles', [AdminUserController::class, 'updateUserRoles']);
		$router->put('admin/promotions/reset', [PromotionController::class, 'resetPromotions']);
	});
