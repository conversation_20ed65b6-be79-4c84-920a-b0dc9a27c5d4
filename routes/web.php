<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

//Route::get('/', function () {
//    return view('welcome');
//});

//Auth::routes(['verify' => true]);
Route::get('verify/{id}', 'Auth\VerificationController@verify')
    ->name('verification.verify');
Route::post('password/forgot', 'Auth\ForgotPasswordController@sendResetLinkEmail')
    ->name('forgot.password');
Route::post('password/reset', 'Auth\ResetPasswordController@reset')
    ->name('password.reset');
Route::post('email/resend', 'Auth\VerificationController@resend')
    ->name('email.resend');
