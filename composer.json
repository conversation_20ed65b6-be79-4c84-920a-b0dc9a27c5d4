{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.4", "ext-json": "*", "ext-simplexml": "*", "ext-soap": "*", "ext-zip": "*", "artisaninweb/laravel-soap": "********", "dingo/api": "^3.0", "endroid/qr-code": "^4.2", "epigra/tckimlik": "^1.0", "felixkiss/uniquewith-validator": "^3.4", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.1", "intervention/image": "^2.5", "laravel/framework": "^8.0", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "maatwebsite/excel": "^3.1", "silber/bouncer": "^1.0", "takdeniz/laravel-likable-comment": "dev-master", "tecnickcom/tcpdf": "^6.4", "tymon/jwt-auth": "^1.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.7", "facade/ignition": "^2.3.6", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.1", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}