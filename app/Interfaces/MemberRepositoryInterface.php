<?php
namespace App\Interfaces;

/**
 * Interface MemberRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface MemberRepositoryInterface extends ResourceRepositoryInterface, UserInterface
{
	public function getMemberByRole($teamId, $role);

	public function assignPicture($assetId);

	public function getTeamMembers($teamId);

	public function makeQRCode();

	public function makeQrCodeForAllMembers();

	public function getAllMembers();

	public function makeQrCodeForTeam($teamId);

	public function findMemberBy($where);


}
