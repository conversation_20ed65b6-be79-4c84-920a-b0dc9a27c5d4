<?php
namespace App\Interfaces;

/**
 * Interface DomesticPartsRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface TechnicalDesignRepositoryInterface extends ResourceRepositoryInterface
{

	public function importOld($collections);

	public function import($collection);

	public function calculateTTRTotal($team);

	public function update($data);

	public function getByTeam($teamId);

	public function updateOld($data);

	public function truncate();
}
