<?php
namespace App\Interfaces;

use App\Interfaces\Repository\FindMainModelInterface;
use App\Interfaces\Repository\FindOrFailMainModelInterface;
use App\Interfaces\Repository\GetAllMainModelInterface;
use App\Models\User;

/**
 * Interface UserRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface UserRepositoryInterface extends BaseRepositoryInterface, GetAllMainModelInterface,
	FindMainModelInterface, FindOrFailMainModelInterface
{
	/**
	 * @return UserDetailRepositoryInterface
	 */
	public function getUserDetailRepo(): UserDetailRepositoryInterface;

	/**
	 * creates user
	 *
	 * @param $data
	 * @return User
	 */
	public function createUser($userData, $userDetail = null);

	/**
	 * @param      $userData
	 * @param null $userDetail
	 * @return mixed
	 */
	public function updateUser($userData, $userDetail = null);

	/**
	 * @param $email
	 * @return User
	 */
	public function getUserByEmail($email);

	/**
	 * @param $roles
	 * @return mixed
	 */
	public function assignRole($roles);

	/**
	 * @param User $user
	 * @return User
	 */
	public function removeUser(User $user);

	/**
	 * @return bool
	 */
	public function isProfileCompleted();

	/**
	 * @param $assetId
	 * @return mixed
	 */
	public function assignProfilePicture($assetId);

	/**
	 * @return mixed
	 */
	public function getAllRoles();

}
