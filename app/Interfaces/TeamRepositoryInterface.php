<?php
namespace App\Interfaces;

/**
 * Interface TeamRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface TeamRepositoryInterface extends ResourceRepositoryInterface, FiltersInterface
{

	public function attachFile($fileId, $type = null);

	public function getTeamsWithSticker();

	public function approve();

	public function import($collection);

	public function findTeamByCaptain($captainEmail);

	public function findByTeamNo($teamId);

	public function giveVehicleNumber();

	public function checkTeam();

}
