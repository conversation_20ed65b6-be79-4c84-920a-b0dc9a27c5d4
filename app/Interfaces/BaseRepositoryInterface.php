<?php
namespace App\Interfaces;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Interface BaseModelInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface BaseRepositoryInterface
{
	/**
	 * @return Model|Builder
	 */
	public function getMainModel();

	/**
	 * @param Model $mainModel
	 * @return $this
	 */
	public function setMainModel(Model $mainModel);

	/**
	 * @param $id
	 * @return $this
	 */
	public function findMainModel($id);

	/**
	 * @param $id
	 * @return $this
	 */
	public function findMainModelOrFail($id);
}
