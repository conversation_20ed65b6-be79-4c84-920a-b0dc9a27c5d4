<?php

namespace App\Interfaces\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Interface FindOrFailMainModelInterface
 *
 * @package App\Interfaces\Repository
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface FindOrFailMainModelInterface
{
	/**
	 * @param      $id
	 * @param null $message
	 * @return Builder|Model|null|mixed
	 */
	public function findOrFail($id, $message = null);

}
