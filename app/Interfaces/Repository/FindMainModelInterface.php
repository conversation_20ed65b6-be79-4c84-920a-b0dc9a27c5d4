<?php

namespace App\Interfaces\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Interface FindMainModelInterface
 *
 * @package App\Traits\Repository
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface FindMainModelInterface
{
	/**
	 * @param $id
	 * @return Builder|\Illuminate\Database\Eloquent\Collection|Model|null|mixed
	 */
	public function find($id);

}
