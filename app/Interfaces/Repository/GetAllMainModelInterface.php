<?php

namespace App\Interfaces\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Interface GetAllMainModel
 *
 * @package App\Traits\Repository
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface GetAllMainModelInterface
{
	/**
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection|Model[]
	 */
	public function getAll();

}
