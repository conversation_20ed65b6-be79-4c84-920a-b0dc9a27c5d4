<?php
namespace App\Interfaces;

use Actuallymab\LaravelComment\Contracts\Commentable;
use App\Interfaces\Repository\FindOrFailMainModelInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Tak<PERSON>iz\LikableComment\Models\Comment;

/**
 * Interface CommentRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface CommentRepositoryInterface extends
	BaseRepositoryInterface,
	FindOrFailMainModelInterface,
	UserInterface
{
	/**
	 * @param Model $model
	 * @return Comment[]|Collection
	 */
	public function getComments(Commentable $model);

	/**
	 * @param Commentable $topic
	 * @param             $comment
	 * @return mixed
	 */
	public function comment(Commentable $topic, $comment);

	/**
	 * @return mixed
	 */
	public function like();

	/**
	 * @return mixed
	 */
	public function dislike();

}
