<?php
namespace App\Interfaces;

use App\Interfaces\Repository\FindOrFailMainModelInterface;
use App\Models\Asset;

/**
 * Interface AssetRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface AssetRepositoryInterface extends UserInterface, FindOrFailMainModelInterface
{
	/**
	 * Upload asset from request
	 *
	 * @param $file
	 * @return Asset
	 */
	public function upload($file);

	/**
	 * @param $assetId
	 * @return mixed
	 */
	public function findUserAssetOrFail($assetId);

	/**
	 * @param $id
	 * @return mixed
	 */
	public function deleteAsset($id);

	/**
	 * @param $assetId
	 * @param $type
	 * @return mixed
	 */
	public function addSystemFile($assetId, $type);

	/**
	 * @return mixed
	 */
	public function systemFiles();

}
