<?php
namespace App\Interfaces;

/**
 * Interface EvaluationRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface EvaluationRepositoryInterface extends ResourceRepositoryInterface, UserInterface
{
	public function create($data);

	public function createAfterRaceDetails($sessionId = null);

	public function createAfterRaceDetailsMulti($teamIds, $sessionId);

	public function createDetail($evaluation, $criterias = null, $sessionId = null);

	public function close();

	public function reopen();

	public function export($path = null);

	public function appointmentRejectOperation($teamId);

	public function findOrFailDetail($id);

	public function updateDetail($id, $data);

	public function checkSticker($onlyRequired = true);

	public function giveSticker();

	public function getEvalAnalytics();
}
