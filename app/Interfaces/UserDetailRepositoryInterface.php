<?php
namespace App\Interfaces;

/**
 * Interface UserSettingRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> A<PERSON> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface UserDetailRepositoryInterface extends UserInterface, BaseRepositoryInterface
{

	/**
	 * @param $userId
	 * @return mixed
	 */
	public function update($data);

	/**
	 * @return mixed
	 */
	public function get();

	/**
	 * @param $userDetails
	 * @return mixed
	 */
	public function createUserDetails($userDetails);

	/**
	 * @param $userDetails
	 * @return mixed
	 */
	public function updateUserDetails($userDetails);

}
