<?php
namespace App\Interfaces;

use App\Models\SessionTeam;

/**
 * Interface RaceSessionRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface RaceSessionRepositoryInterface extends ResourceRepositoryInterface
{
	/**
	 * @param $teams
	 * @return \Illuminate\Support\Collection|SessionTeam[]
	 */
	public function addRaceTeam($teams);

	/**
	 * @param SessionTeam $raceTeam
	 * @param          $data
	 * @return SessionTeam
	 */
	public function updateRaceTeam(SessionTeam $raceTeam, $data);

}
