<?php
namespace App\Interfaces;

use App\Interfaces\Repository\FindOrFailMainModelInterface;
use App\Interfaces\Repository\GetAllPaginateInterface;
use App\Interfaces\Repository\MainModelMethodsInterface;

/**
 * Interface PromotionRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface PromotionRepositoryInterface extends ResourceRepositoryInterface
{
	public function getTeamPromotions($teamId);

	public function generatePromotionForTeam($teamId);

}
