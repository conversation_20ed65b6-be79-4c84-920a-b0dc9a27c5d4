<?php
namespace App\Interfaces;

use App\Interfaces\Repository\CreateMainModelInterface;
use App\Interfaces\Repository\GetAllMainModelInterface;

/**
 * Interface SettingsRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface SettingsRepositoryInterface extends BaseRepositoryInterface
	, CreateMainModelInterface, GetAllMainModelInterface
{
	/**
	 * @param      $key
	 * @param null $default
	 * @return mixed
	 */
	public function get($key, $default = null);

}
