<?php
namespace App\Interfaces;

use Illuminate\Notifications\DatabaseNotification;

/**
 * Interface NotificationRepositoryInterface
 *
 * @package App\Interfaces
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
interface NotificationRepositoryInterface extends UserInterface
{
	/**
	 * @param $id
	 * @return DatabaseNotification|mixed
	 */
	public function markAsRead($id);

	/**
	 * @return DatabaseNotification[]|mixed
	 */
	public function notifications();

	/**
	 * @return DatabaseNotification[]|mixed
	 */
	public function readNotifications();

	/**
	 * @return DatabaseNotification[]|mixed
	 */
	public function unreadNotifications();
}
