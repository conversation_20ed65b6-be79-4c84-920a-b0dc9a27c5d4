<?php

namespace App\Notifications;

use App\Events\UserCreated;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeNotification extends Notification
{
	use Queueable;

	protected $event;

	/**
	 * Create a new notification instance.
	 *
	 * @return void
	 */
	public function __construct(UserCreated $event)
	{
		$this->event = $event;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param mixed $notifiable
	 * @return array
	 */
	public function via($notifiable)
	{
		return ['database', 'mail'];
	}

	/**
	 * Get the mail representation of the notification.
	 *
	 * @param mixed $notifiable
	 * @return \Illuminate\Notifications\Messages\MailMessage
	 */
	public function toMail($notifiable)
	{
		return (new MailMessage)
			->subject(\Lang::get('Welcome to Tubitak Efficiency Challenge'))
			->greeting(\Lang::get('Welcome'))
			->line(\Lang::get("Panel Address : :panelAddress", ['panelAddress' => env('FRONTEND_ADDRESS', 'http://localhost:4200')]))
			->line(\Lang::get("username : :userName", ['userName' => $this->event->user->email]))
			->line(\Lang::get("password : :password", ['password' => $this->event->pass]))
			->action(\Lang::get('Login Here'), env('FRONTEND_ADDRESS', 'http://localhost:4200'));
	}

	/**
	 * Get the array representation of the notification.
	 *
	 * @param mixed $notifiable
	 * @return array
	 */
	public function toArray($notifiable)
	{
		return [
			'email' => $notifiable->email
		];
	}
}
