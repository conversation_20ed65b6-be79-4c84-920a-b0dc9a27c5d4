<?php

namespace App\Notifications;

use App\Events\EvaluationClosed;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EvaluationStatusNotification extends Notification
{
	use Queueable;

	protected $event;
	/**
	 * @var array|mixed
	 */
	protected $attachments;

	/**
	 * Create a new notification instance.
	 *
	 * @return void
	 */
	public function __construct(EvaluationClosed $event, $attachments = [])
	{
		$this->event       = $event;
		$this->attachments = $attachments;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param mixed $notifiable
	 * @return array
	 */
	public function via($notifiable)
	{
		return ['database', 'mail'];
	}

	/**
	 * Get the mail representation of the notification.
	 *
	 * @param mixed $notifiable
	 * @return \Illuminate\Notifications\Messages\MailMessage
	 */
	public function toMail($notifiable)
	{
		$mailMessage = (new MailMessage)
			->subject(\Lang::get('Tubitak Efficiency Challenge Evaluation Session Done'))
			->greeting(\Lang::get(''))
			->line(\Lang::get("Bir değerlendirme daha sona erdi, ekte takımınız değerlendirme dosyalarını bulabilirsiniz"));

		foreach ($this->attachments as $attachment) {
			$mailMessage->attach($attachment);
		}

		return $mailMessage;
	}

	/**
	 * Get the array representation of the notification.
	 *
	 * @param mixed $notifiable
	 * @return array
	 */
	public function toArray($notifiable)
	{
		return [
			'email'       => $notifiable->email,
			'attachments' => $this->attachments
		];
	}

}
