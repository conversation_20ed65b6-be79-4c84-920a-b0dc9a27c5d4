<?php
namespace App\Notifications;

use App\Helpers\Helper;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

/**
 * Class ResetPasswordNotification
 *
 * @package App\Notifications
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class ResetPasswordNotification extends ResetPassword implements ShouldQueue
{
	use Queueable;

	/**
	 * @param mixed $notifiable
	 * @return \Illuminate\Notifications\Messages\MailMessage
	 */
	public function toMail($notifiable)
	{
//		$baseUrl = url(config('app.url') . route('password.reset', ['token' => $this->token, 'email' => $notifiable->getEmailForPasswordReset()], false));
		$data = ['token' => $this->token, 'email' => $notifiable->getEmailForPasswordReset()];

		if ($pattern = \Config::get('settings.reset_password_wrapper')) {
			$baseUrl = Helper::build($pattern, [
				'url' => urlencode(json_encode($data))
			]);
		}

		return parent::toMail($notifiable)
			->action(Lang::get('Reset Password'), $baseUrl);
	}
}
