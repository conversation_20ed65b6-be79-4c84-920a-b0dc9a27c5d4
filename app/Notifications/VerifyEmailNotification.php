<?php
namespace App\Notifications;

use App\Helpers\Helper;
use Config;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Lang;

/**
 * Class VerifyEmailNotification
 *
 * @package App\Helpers
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class VerifyEmailNotification extends VerifyEmail implements ShouldQueue
{
	use Queueable;

	/**
	 * @param mixed $notifiable
	 * @return string
	 */
	protected function verificationUrl($notifiable)
	{
		$baseUrl = parent::verificationUrl($notifiable);

		if ($pattern = \Config::get('settings.verify_email_wrapper')) {
			$baseUrl = Helper::build($pattern, [
				'url' => urlencode($baseUrl)
			]);
		}

		return $baseUrl;
	}

	/**
	 * Build the mail representation of the notification.
	 *
	 * @param mixed $notifiable
	 * @return \Illuminate\Notifications\Messages\MailMessage
	 */
	public function toMail($notifiable)
	{
		$verificationUrl = $this->verificationUrl($notifiable);

		if (static::$toMailCallback) {
			return call_user_func(static::$toMailCallback, $notifiable, $verificationUrl);
		}

		return (new MailMessage)
			->subject(Lang::get('Verify Email Address'))
			->line(Lang::get('Please click the button below to verify your email address.'))
			->action(Lang::get('Verify Email Address'), $verificationUrl)
			->line('This link will expire in ' .
				Config::get('auth.verification.expire', 60)
				. ' minutes, but you can request a new verification link in case it expired already.')
			->line('After the successful registration, you will be able to login with your login credentials.')
			->line('Thanks for joining ' . \Config::get('app.name'))
			->line(Lang::get('If you did not create an account, no further action is required.'));
	}
}
