<?php
namespace App\Models;

/**
 * Class App
 *
 * @package App\Models
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateSettingsTable
 *
 */
class Settings extends BaseModel
{
	/**
	 * @var array
	 */
	protected $guarded = [];

	/**
	 * @var array
	 */
	protected $hidden = [];
	protected $casts  = ['value' => 'array'];// it must be filled because of hasCast()

	protected $keyTypes = [
		'appointment_time'       => 'integer',
		'appointment_slot_limit' => 'integer',
		'promotions'             => 'array',
		'max_domestic_penalty'   => 'integer',
	];

	protected function getCastType($key)
	{
		if ($key == 'value' && (!empty($this->type) || isset($this->keyTypes[$this->key]))) {
			return $this->type ? $this->type : $this->keyTypes[$this->key];
		} else {
			return parent::getCastType($key);
		}
	}
}
