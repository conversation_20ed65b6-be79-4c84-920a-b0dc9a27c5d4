<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BaseModel
 *
 * @package App\Models
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method static |BaseModel|Builder ordered()
 * @method Builder|static query()
 */
abstract class BaseModel extends Model
{
	protected $perPage  = 30;
	protected $orderWay = 'DESC';

	/**
	 * @param $query
	 * @return mixed
	 */
	public function scopeOrdered($query)
	{
		return $query->orderBy($this->getKeyName(), $this->orderWay);
	}
}
