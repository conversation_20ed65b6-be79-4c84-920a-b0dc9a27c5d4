<?php

namespace App\Models;

/**
 * Class Member
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateMembersTable migration
 * @property HesCode hesCodeResult
 */
class Member extends BaseModel
{
	protected $guarded = [];
	protected $appends = ['picture_url',];
	protected $hidden  = ['picture',];

	public function team()
	{
		return $this->belongsTo(Team::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\MorphOne
	 */
	public function picture()
	{
		return $this->morphOne(UserAsset::class, 'mediable')
			->orderBy('updated_at', 'DESC');
	}

	public function hesCodeResult()
	{
		return $this->hasOne(HesCode::class, 'hes_code', 'hes_code')
			->orderBy('updated_at', 'DESC');
	}

	/**
	 * appended!
	 *
	 * @param $pictureUrl
	 * @return string|null
	 */
	public function getPictureUrlAttribute($pictureUrl)
	{
		if ($this->picture && $this->picture->asset) {
			return $this->picture->asset->url;
		}

		return null;
	}

	public function getHesCodeAttribute($value)
	{
		return strtoupper($value);
	}
}
