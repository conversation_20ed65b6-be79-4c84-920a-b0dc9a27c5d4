<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class UserAsset
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @property Asset asset
 * @see     \CreateUserAssetsTable migration
 */
class UserAsset extends BaseModel
{
	use SoftDeletes;

	protected $guarded = [];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function asset()
	{
		return $this->belongsTo(Asset::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\MorphTo
	 */
	public function mediable()
	{
		return $this->morphTo();
	}
}
