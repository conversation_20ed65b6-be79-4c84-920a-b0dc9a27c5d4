<?php

namespace App\Models;

use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

/**
 * Class Appointment
 *
 * @package App\Models
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateAppointmentsTable migration
 *
 * @property Team team
 */
class Appointment extends BaseModel implements WithHeadingRow, WithMultipleSheets
{
	protected $guarded = [];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function team()
	{
		return $this->belongsTo(Team::class);
	}

	public function sheets(): array
	{
		return [
			new static(),
		];
	}

}
