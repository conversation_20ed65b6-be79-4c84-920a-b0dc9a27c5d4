<?php

namespace App\Models;

/**
 * Class DomesticPart
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateTechnicalDesignsTable migration
 */
class TechnicalDesign extends BaseModel
{
	protected $guarded = [];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function team()
	{
		return $this->belongsTo(Team::class);
	}
}
