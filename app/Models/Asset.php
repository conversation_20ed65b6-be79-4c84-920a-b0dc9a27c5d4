<?php

namespace App\Models;

use App\Enums\AssetType;
use Config;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Asset
 *
 * @package App\Models
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateAssetsTable migration
 * @property string url
 */
class Asset extends BaseModel
{
	use SoftDeletes;
	protected $dates = ['deleted_at', 'created_at', 'updated_at'];

	protected $guarded = [];

	protected $hidden = [
		'created_by',
		'updated_by',
		'deleted_by',
		'created_at',
		'updated_at',
		'deleted_at',
		'laravel_through_key',
		'pivot'
	];

	public static function boot()
	{
		parent::boot();

		static::deleted(function ($item) {
			if (file_exists($item->fullPath)) {
				unlink($item->fullPath);
			}
		});
	}

	/**
	 * @param $url
	 * @return string
	 */
	public function getUrlAttribute($url)
	{
		return Config::get('app.asset_url') . '/' .
			($this->type == AssetType::IMAGE ? $this->thumbnail : $this->path);
	}
}
