<?php

namespace App\Models;

use App\Enums\GenderType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class UserSetting
 *
 * @package App\Models
 * <AUTHOR> Cilesiz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateUserDetailsTable migration
 */
class UserDetail extends BaseModel
{
	use SoftDeletes;
	protected $guarded = [];
	protected $appends = ['age'];

	protected $hidden = [
		'hidden_birthday',//for public age calculation
		'id',
		'user_id',
		'created_by',
		'updated_by',
		'deleted_by',
		'created_at',
		'updated_at',
		'deleted_at',
	];

	/**
	 * appended!
	 *
	 * @return string|null
	 * @throws \Exception
	 */
	public function getAgeAttribute()
	{
		if ($date = $this->birthday ?: $this->hidden_birthday) {
//			$this->makeHidden('birthday');

			return Carbon::now()->diffInYears(new Carbon($date));
		}

		return null;
	}

	/**
	 * @param $gender
	 * @return |null
	 */
	public function getGenderAttribute($gender)
	{
		return GenderType::toHumanReadable($gender);
	}

}
