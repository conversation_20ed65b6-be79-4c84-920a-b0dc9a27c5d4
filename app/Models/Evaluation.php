<?php

namespace App\Models;

use App\Enums\EvaluationCompatibility;
use App\Traits\BelongsToTeamTrait;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class Evaluation
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateEvaluationsTable migration
 *
 * @property Team                          team
 * @property EvaluationDetail[]|Collection details
 */
class Evaluation extends BaseModel
{
	use BelongsToTeamTrait;

	protected $guarded = [];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\hasMany
	 */
	public function forms()
	{
		return $this->hasMany(Form::class, 'id', 'team_id');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function details()
	{
		return $this->hasMany(EvaluationDetail::class);
	}

	public function collection()
	{
		return static::all();
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\hasMany
	 */
	public function technicalDesign()
	{
		return $this->hasMany(TechnicalDesign::class, 'team_id', 'team_id');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
	 */
	public function dynamicDrive()
	{
		return $this->hasOneThrough(Criteria::class, EvaluationDetail::class,
			'evaluation_id', 'id',
			'id', 'criteria_id',
		)
			->where('no', '13.1')
			->where('compatibility', EvaluationCompatibility::APPROVE);
	}

}
