<?php

namespace App\Models;

use App\Enums\EvaluationCompatibility;
use App\Enums\PenaltyType;
use App\Helpers\EvalHelper;

/**
 * Class EvaluationDetail
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateEvaluationDetailsTable migration
 *
 * @property Team     team
 * @property Criteria criteria
 */
class EvaluationDetail extends BaseModel
{
	protected $guarded = [];
	protected $hidden  = ['penalty_id', 'penaltyRel'];
	protected $appends = ['penalty', 'is_required', 'allow_penalty'];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function team()
	{
		return $this->belongsTo(Team::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function evaluation()
	{
		return $this->belongsTo(evaluation::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function penaltyRel()
	{
		return $this->belongsTo(Penalty::class, 'penalty_id', 'id');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasOne
	 */
	public function criteria()
	{
		return $this->hasOne(Criteria::class, 'id', 'criteria_id');
	}

	public function getPenaltyAttribute()
	{
		return $this->penaltyRel ? $this->penaltyRel->penalty : 0;
	}

	/**
	 * used in append
	 *
	 * @return bool
	 */
	public function getIsRequiredAttribute()
	{
		return $this->criteria && $this->criteria->is_required && $this->active;
	}

	/**
	 * used in append
	 *
	 * @return bool
	 */
	public function getAllowPenaltyAttribute()
	{
		$function = null;
		switch ($this->compatibility) {
			case EvaluationCompatibility::APPROVE:
				$function = $this->criteria->allow_approve;
				break;
			case EvaluationCompatibility::DEFECTIVE:
				$function = $this->criteria->allow_defective;
				break;
			case EvaluationCompatibility::DISQUALIFIED:
				$function = $this->criteria->allow_disqualified;
				break;
		}

		$parsed = EvalHelper::parseFunction(trim($function));

		return $parsed;
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function updatedBy()
	{
		return $this->belongsTo(User::class, 'updated_by');
	}

	public function setPenaltyAttribute($penalty)
	{
		if ($this->penaltyRel) {
			if ($penalty == 0) {
				$this->penaltyRel->delete();
				$this->penalty_id = null;
				$this->setRelation('penaltyRel', null);

				return;
			}
			$this->penaltyRel->penalty = $penalty;
			$this->penaltyRel->save();

			return;
		}
		if ($penalty == 0) {
			return;
		}

		if ($this->after_race) {
			$type = PenaltyType::AFTER;
		} else {
			$type = PenaltyType::BEFORE;
		}

		$penob = $this->penaltyRel()->create([
			'penalty'    => $penalty,
			'team_id'    => $this->evaluation->team_id,
			'session_id' => $this->evaluation->after_race_started,
			'subject'    => '#' . $this->criteria->no . ' - ' . $this->criteria->sub_category,
			'violation'  => $this->criteria->subject . "\n" . $this->criteria->content,
			'conclusion' => $this->notes,
			'type'       => $type,

		]);

		$this->setRelation('penaltyRel', $penob);
		$this->penalty_id = $penob->id;

		return;
	}
}
