<?php

namespace App\Models;

use Actuallymab\LaravelComment\Contracts\Commentable;
use Actuallymab\LaravelComment\HasComments;
use App\Traits\BelongsToTeamTrait;
use App\Traits\HasAttachmentsTrait;
use App\Traits\HasLikesTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Form
 *
 * @package App\Models
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreatePetitionsTable migration
 */
class Petition extends Model implements Commentable
{
	protected $guarded = [];

	use HasAttachmentsTrait;

	use HasComments;
	use HasLikesTrait;
	use BelongsToTeamTrait;

	public function canBeRated(): bool
	{
		return true; // default false
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function session()
	{
		return $this->belongsTo(Session::class);
	}
}
