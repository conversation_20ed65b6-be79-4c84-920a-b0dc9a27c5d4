<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;

/**
 * Class Race
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateRacesTable migration
 * @property Session[]|Collection sessions
 */
class Race extends BaseModel
{
	protected $guarded = [];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function sessions()
	{
		return $this->hasMany(Session::class);
	}

	public function scores()
	{
		return $this->hasManyThrough(SessionTeam::class, Session::class,
			'race_id', 'session_id'
		)->where('best', 1)
			->orderBy('score', 'desc');
	}

	public function allScores()
	{
		return $this->hasManyThrough(SessionTeam::class, Session::class,
			'race_id', 'session_id'
		)->orderBy('score', 'desc');
	}
}
