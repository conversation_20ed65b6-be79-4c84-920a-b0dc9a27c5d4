<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;

/**
 * Class Session
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateSessionsTable migration
 * @property SessionTeam[]|Collection raceTeams
 */
class Session extends BaseModel
{
	protected $guarded = [];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function raceTeams()
	{
		return $this->hasMany(SessionTeam::class)
			->orderBy('score', 'desc');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function race()
	{
		return $this->belongsTo(Race::class);
	}
}
