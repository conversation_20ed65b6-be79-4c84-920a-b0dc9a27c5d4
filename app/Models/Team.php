<?php

namespace App\Models;

use App\Enums\EvaluationStatus;
use App\Enums\MemberType;
use App\Enums\SchoolType;
use App\Repositories\TeamRepository;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

/**
 * Class Asset
 *
 * @package App\Models
 * <AUTHOR> Ak<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateTeamsTable migration
 * @property UserAsset[]|Collection   files
 * @property Appointment[]|Collection appointments
 */
class Team extends BaseModel implements WithHeadingRow, WithMultipleSheets
{
	protected $guarded = [];
	protected $appends = ['logo_url', 'school_type_name'];
	protected $hidden  = ['logo'];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function appointments()
	{
		return $this->hasMany(Appointment::class);
//			->where('status', AppointmentStatusType::ACTIVE);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasOne
	 */
	public function currentEvaluation()
	{
		return $this->hasOne(Evaluation::class)
			->orderBy('id', 'desc');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function evaluations()
	{
		return $this->hasMany(Evaluation::class);
	}

	public function stickers()
	{
		return $this->evaluations()->where('status', EvaluationStatus::SUCCESS);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function promotions()
	{
		return $this->hasMany(Promotion::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function penalties()
	{
		return $this->hasMany(Penalty::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function forms()
	{
		return $this->hasMany(Form::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function members()
	{
		return $this->hasMany(Member::class)
			->orderBy('in_area', 'desc')
			->orderBy('first_name');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function membersInArea()
	{
		return $this->hasMany(Member::class)
			->where('in_area', true);
	}

	public function academicAdvisor()
	{
		return $this->hasOne(Member::class)
			->where('role_in_team', MemberType::AcademicAdvisor);
	}

	public function driver()
	{
		return $this->hasOne(Member::class)
			->where('role_in_team', MemberType::Driver);
	}

	public function reserveDriver()
	{
		return $this->hasOne(Member::class)
			->where('role_in_team', MemberType::ReserveDriver);
	}

	/**
	 * @inheritDoc
	 */
	public function sheets(): array
	{
		return [
			new static(),
			new static(),
			new static(),
		];
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\MorphMany
	 */
	public function files()
	{
		return $this->morphMany(UserAsset::class, 'mediable');
	}

	public function photo()
	{
		return $this->morphOne(UserAsset::class, 'mediable')->orderBy('id', 'DESC')->where('type', 'team_photo');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasOne
	 */
	public function logo()
	{
		return $this->hasOne(Asset::class, 'id', 'logo_id');
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasOne
	 */
	public function captain()
	{
		return $this->hasOne(User::class, 'email', 'team_leader_email');
	}

	/**
	 * appended!
	 *
	 * @param $logoUrl
	 * @return string|null
	 */
	public function getLogoUrlAttribute($logoUrl)
	{
		if ($this->logo) {
			return $this->logo->url;
		}

		return null;
	}

	public function getTeamStatusAttribute($status)
	{
		return app(TeamRepository::class)
			->setMainModel($this)
			->checkTeam();
	}

	public function getSchoolTypeNameAttribute($schoolType)
	{
		return SchoolType::toHumanReadable($this->school_type);
	}


//	/**
//	 * @return \Illuminate\Database\Eloquent\Relations\MorphToMany
//	 */
//	public function attachments()
//	{
//		return $this->morphToMany(Asset::class, 'mediable', (new UserAsset)->getTable());
//	}

}
