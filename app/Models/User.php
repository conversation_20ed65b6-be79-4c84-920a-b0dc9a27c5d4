<?php
namespace App\Models;

use Actuallymab\LaravelComment\CanComment;
use App\Interfaces\UserRepositoryInterface;
use App\Notifications\ResetPasswordNotification;
use App\Notifications\VerifyEmailNotification;
use App\Traits\CanLikeTrait;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Silber\Bouncer\Database\HasRolesAndAbilities;
use Takdeniz\LikableComment\Traits\CanLikeComment;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * Class User
 *
 * @package App\Models
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateUsersTable migration
 * @property UserDetail details
 * @property UserAsset  picture
 */
class User extends BaseModel implements
	AuthenticatableContract,
	AuthorizableContract,
	CanResetPasswordContract,
	JWTSubject,
	MustVerifyEmail
//	MustVerifyPhoneContract
{
	use \Illuminate\Auth\Authenticatable, Authorizable, CanResetPassword, \Illuminate\Auth\MustVerifyEmail;

	use SoftDeletes;
	use Notifiable;
	use HasRolesAndAbilities;

//	use MustVerifyPhone;

	use CanComment;
	use CanLikeComment;
	use CanLikeTrait;

	/**
	 * The attributes that are mass assignable.
	 *
	 * @var array
	 */
	protected $fillable = [
		'first_name', 'last_name', 'email', 'password', 'phone_number',
	];

	/**
	 * The attributes that should be hidden for arrays.
	 *
	 * @var array
	 */
	protected $hidden = [
		'password', 'remember_token', 'picture', 'roles'
	];

	/**
	 * The attributes that should be cast to native types.
	 *
	 * @var array
	 */
	protected $casts = [
		'email_verified_at' => 'datetime',
	];

	protected $appends = ['picture_url',];

	/**
	 * Send the email verification notification.
	 *
	 * @return void
	 */
	public function sendEmailVerificationNotification()
	{
		if (!\Config::get('settings.send_verification_email')) {
			return;
		}

		$this->notify(new VerifyEmailNotification());
	}

	/**
	 * Send the password reset notification.
	 *
	 * @param string $token
	 * @return void
	 */
	public function sendPasswordResetNotification($token)
	{
		$this->notify(new ResetPasswordNotification($token));
	}

	/**
	 * Get the identifier that will be stored in the subject claim of the JWT.
	 *
	 * @return mixed
	 */
	public function getJWTIdentifier()
	{
		return $this->getKey();
	}

	/**
	 * Return a key value array, containing any custom claims to be added to the JWT.
	 *
	 * @return array
	 */
	public function getJWTCustomClaims()
	{
		return [];
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function assets()
	{
		return $this->hasMany(asset::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasOne
	 */
	public function details()
	{
		return $this->hasOne(UserDetail::class);
	}

	/**
	 * gets user dominant role
	 *
	 * @return string
	 */
	public function getRoleAttribute()
	{
		return $this->roles->pluck('name');
//		return $this->isA(Roles::SUPERADMIN) ? Roles::SUPERADMIN : Roles::USER;
	}

	/**
	 * gets user dominant role
	 *
	 * @return string
	 */
	public function getProfileCompletedAttribute()
	{
		return app(UserRepositoryInterface::class)->setMainModel($this)->isProfileCompleted();
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\MorphOne
	 */
	public function picture()
	{
		return $this->morphOne(UserAsset::class, 'mediable');
	}

	/**
	 * appended!
	 *
	 * @param $pictureUrl
	 * @return string|null
	 */
	public function getPictureUrlAttribute($pictureUrl)
	{
		if ($this->picture && $this->picture->asset) {
			return $this->picture->asset->url;
		}

		return null;
	}

	public function getFullNameAttribute()
	{
		return $this->first_name . ' ' . $this->last_name;
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\HasOne
	 */
	public function team()
	{
		return $this->hasOne(Team::class, 'team_leader_email', 'email');
//			AppType::isLise() ? 'consultant_email' : 'team_leader_email','email');
	}

}
