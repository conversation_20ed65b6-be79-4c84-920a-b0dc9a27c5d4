<?php

namespace App\Models;

/**
 * Class Race
 *
 * @package App\Models
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreateSessionTeamsTable migration
 *
 * @property Team team
 */
class SessionTeam extends BaseModel
{
	protected $guarded = [];
	protected $appends = ['penalty'];

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function team()
	{
		return $this->belongsTo(Team::class);
	}

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function session()
	{
		return $this->belongsTo(Session::class);
	}

	public function getPenaltyAttribute()
	{
		$penalty = 0;
		if ($this->team) {
			$penalty = $this->team->penalties()
				->where(function($query){
					$query->whereNull('session_id')
						->orWhereIn('session_id', [0, $this->session_id]);
				})
				->sum('penalty');
		}

//		$this->penalty = $penalty;

		return $penalty;
	}
}
