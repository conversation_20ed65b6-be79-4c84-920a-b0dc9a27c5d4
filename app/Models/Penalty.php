<?php

namespace App\Models;

use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

/**
 * Class Penalty
 *
 * @package App\Models
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     \CreatePenaltiesTable migration
 */
class Penalty extends BaseModel implements WithCalculatedFormulas, WithHeadingRow
{
	protected $guarded = [];

	public function team()
	{
		return $this->belongsTo(Team::class);
	}
}
