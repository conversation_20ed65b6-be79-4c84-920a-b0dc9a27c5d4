<?php
namespace App\Traits;

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use JWTAuth;

/**
 * Trait RegistersPhoneNumberUsers
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait RegistersPhoneNumberUsers
{
	use LoginResponseTrait;

	/**
	 * @return array
	 */
	protected function validators()
	{

		return [
			'first_name'   => ['required', 'string', 'max:64', 'min:2'],
			'last_name'    => ['required', 'string', 'max:64', 'min:2'],
			'phone_number' => ['required', 'string', 'phone:LENIENT', 'unique:users'],
			'password'     => ['required', 'string', 'min:8'],
		];
	}

	/**
	 * Handle a registration request for the application.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Validation\ValidationException
	 */
	public function register(Request $request)
	{
		$validated = $this->validate();

		/** @var $user User */
		event(new Registered($user = $this->create($validated)));
		if (\Config::get('settings.required_user_confirmation') && !$user->hasVerifiedPhone()) {
			return $this->response->accepted();
		}

		return $this->registered($request, $user);
	}

	/**
	 * The user has been registered.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @param mixed                    $user
	 * @return mixed
	 */
	protected function registered(Request $request, $user)
	{
		$token = JWTAuth::fromUser($user);

		return $this->response->array($this->tokenResponse($token, $user));
	}
}
