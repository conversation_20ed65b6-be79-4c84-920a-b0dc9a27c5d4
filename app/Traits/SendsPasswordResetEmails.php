<?php

namespace App\Traits;

use Dingo\Api\Http\Response;
use Illuminate\Http\Request;

/**
 * Trait SendsPasswordResetEmails
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait SendsPasswordResetEmails
{
	use \Illuminate\Foundation\Auth\SendsPasswordResetEmails;

	/**
	 * Validate the email for the given request.
	 *
	 * @param Request $request
	 * @return void
	 */
	protected function validateEmail(Request $request)
	{
		$this->validate(['email' => 'required|email']);
	}

	/**
	 * Get the response for a successful password reset link.
	 *
	 * @param Request $request
	 * @param string  $response
	 * @return Response
	 */
	protected function sendResetLinkResponse(Request $request, $response)
	{
		return $this->response->noContent();
	}

	/**
	 * Get the response for a failed password reset link.
	 *
	 * @param Request $request
	 * @param string  $response
	 */
	protected function sendResetLinkFailedResponse(Request $request, $response)
	{
		$this->response->error(trans($response), 422);
	}

}
