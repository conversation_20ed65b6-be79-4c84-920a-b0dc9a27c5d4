<?php
	namespace App\Traits;

	use Dingo\Api\Exception\ResourceException;
	use Illuminate\Http\Request;
	use Illuminate\Support\Facades\Validator;
	use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

	/**
	 * Trait ValidateTrait
	 *
	 * @package App\Traits
	 * <AUTHOR> Ak<PERSON>iz <<EMAIL>>
	 * @version 0.1
	 * @since   0.1
	 * @property array validators
	 * @property array validatorMessages
	 */
	trait ValidateTrait
	{
		/**
		 * @param array|null $validators
		 * @param null $request
		 * @return array
		 */
		protected function validate(array $validators = null, $request = null): array
		{
			if (!isset($request)) {
				$request = request();
			}
			$request = $request instanceof Request ? $request->all() : $request;

			$rules = $validators ?: $this->getValidators();

			$validator = Validator::make($request, $rules,
				$this->getValidatorMessages());

			if ($validator->fails()) {
				throw new ResourceException("validation.error", $validator->errors());
			}

			return $validator->validated();
		}


		protected function getValidators(): array
		{
			return method_exists($this, 'validators') ? $this->validators() : [];
		}


		protected function getValidatorMessages(): array
		{
			return property_exists($this, 'validatorMessages') ? $this->validatorMessages : [];
		}

		public function modelNotFound($message = null): void
		{
			throw new NotFoundHttpException($message ?: 'model.not_found');
		}

		public function validationErrors(array $messages): void
		{
			throw new ResourceException("validation.error", $messages);
		}

	}
