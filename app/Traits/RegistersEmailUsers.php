<?php
	namespace App\Traits;

	use App\Models\User;
	use Illuminate\Auth\Events\Registered;
	use Illuminate\Http\Request;
	use Illuminate\Http\Response;
	use JWTAuth;

	trait RegistersEmailUsers
	{
		use LoginResponseTrait;

		protected function validators(): array
		{
			return [
				'first_name' => ['required', 'string', 'max:64', 'min:2'],
				'last_name'  => ['required', 'string', 'max:64', 'min:2'],
				'email'      => ['required', 'string', 'email', 'max:255', 'unique:users'],
				'password'   => ['required', 'string', 'min:8'],
			];
		}

		public function register(Request $request): Response
		{
			$validated = $request->validate($this->validators());

			// create() metodunu ekleyin
			$user = $this->create($validated);

			event(new Registered($user));

			if (\Config::get('settings.required_user_confirmation') && !$user->hasVerifiedEmail()) {
				return $this->response->accepted();
			}

			return $this->registered($request, $user);
		}

		/**
		 * Create a new user instance after a valid registration.
		 */
		protected function create(array $data): User
		{
			return User::create([
				'first_name' => $data['first_name'],
				'last_name' => $data['last_name'],
				'email' => $data['email'],
				'password' => bcrypt($data['password']),
			]);
		}

		protected function registered(Request $request, $user): \Dingo\Api\Http\Response
		{
			$token = JWTAuth::fromUser($user);

			$response = $this->tokenResponse($token, $user);

			return $this->response->array($response);
		}
	}
