<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait CreateMainModel
 *
 * @package App\Traits\repository
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Model|Builder getMainModel(): Model
 */
trait CreateMainModel
{
	/**
	 * @param $data
	 * @return Builder|Model
	 */
	public function create($data)
	{
		return $this->getMainModel()->create($data);
	}
}
