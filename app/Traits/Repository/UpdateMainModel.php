<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait UpdateMainModel
 *
 * @package App\Traits\repository
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait UpdateMainModel
{
	/**
	 * @param $data
	 * @return bool|int
	 */
	public function update($data)
	{
		return $this->getMainModel()->update($data);
	}
}
