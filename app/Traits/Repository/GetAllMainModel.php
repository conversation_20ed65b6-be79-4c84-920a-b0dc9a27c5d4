<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait GetAllMainModel
 *
 * @package App\Traits\repository
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait GetAllMainModel
{
	/**
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection|Model[]
	 */
	public function getAll()
	{
		return $this->getMainModel()->get();
	}

}
