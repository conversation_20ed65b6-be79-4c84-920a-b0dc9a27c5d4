<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait FindMainModel
 *
 * @package App\Traits\repository
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait FindMainModel
{
	/**
	 * @param $id
	 * @return Builder|Builder[]|\Illuminate\Database\Eloquent\Collection|Model|Model[]|null
	 */
	public function find($id)
	{
		return $this->getMainModel()->find($id);
	}

}
