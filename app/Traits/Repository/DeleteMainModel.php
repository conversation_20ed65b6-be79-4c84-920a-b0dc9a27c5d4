<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait DeleteMainModel
 *
 * @package App\Traits\Repository
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Model|Builder getMainModel()
 */
trait DeleteMainModel
{
	/**
	 * @return bool|mixed|null
	 * @throws \Exception
	 */
	public function delete()
	{
		return $this->getMainModel()->delete();
	}

}
