<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Trait FindOrFailMainModel
 *
 * @package App\Traits\Repository
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait FindOrFailMainModel
{
	/**
	 * @param      $id
	 * @param null $message
	 * @return Builder|Model|null|mixed
	 */
	public function findOrFail($id, $message = null)
	{
		$result = $this->getMainModel()->find($id);
		if (!$result) {
			throw new NotFoundHttpException($message ?: 'resource.not_found');
		}

		return $result;
	}

}
