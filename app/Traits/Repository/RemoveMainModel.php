<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait RemoveMainModel
 *
 * @package App\Traits\repository
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait RemoveMainModel
{
	/**
	 * @param $id
	 * @return bool|mixed|null
	 * @throws \Exception
	 */
	public function remove($id)
	{
		return $this->getMainModel()
			->whereKey($id)
			->delete();
	}

}
