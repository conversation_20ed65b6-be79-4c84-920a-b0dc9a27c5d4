<?php

namespace App\Traits\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Trait GetAllPaginateMainModel
 *
 * @package App\Traits\repository
 * <AUTHOR> Ak<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait GetAllPaginateMainModel
{
	/**
	 * @return Builder[]|Collection|Model[]
	 */
	public function getAllPaginate()
	{
		return $this->getMainModel()
			->ordered()
			->paginate();
	}

}
