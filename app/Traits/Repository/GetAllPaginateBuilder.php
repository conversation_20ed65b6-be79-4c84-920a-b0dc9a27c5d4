<?php

namespace App\Traits\Repository;

use App\Traits\EloquentBuilderTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

/**
 * Trait GetAllPaginateBuilder
 *
 * @package App\Traits\repository
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait GetAllPaginateBuilder
{
	protected $defaults = ['limit' => 30];

	use EloquentBuilderTrait;

	/**
	 * @return LengthAwarePaginator|Collection
	 */
	public function getAllPaginate($query = null)
	{
		$resourceOptions = $this->parseResourceOptions();

		if (!$query) {
			$query = $this->getMainModel()->query();
		}

		$this->applyResourceOptions($query, $resourceOptions);

		return $query->paginate($resourceOptions['limit']);
	}

}
