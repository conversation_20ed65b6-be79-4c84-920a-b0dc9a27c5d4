<?php
namespace App\Traits;

use App\Models\PetitionLikes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Trait HasLikeTrait
 *
 * @package Takdeniz\LikableComment\Traits
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait CanLikeTrait
{
	/**
	 * @param Model $model
	 * @return mixed
	 */
	public function likePetition(Model $model)
	{
		return $this->likeOrDislikePetition($model, true);
	}

	/**
	 * @param Model $model
	 * @param bool  $isLiked
	 * @return mixed
	 */
	public function likeOrDislikePetition(Model $model, $isLiked = true)
	{
		$existReaction = $this->petitionReactions()->where('petition_id', $model->id)->first();
		if ($existReaction) {
			if ($existReaction->is_liked === (int)$isLiked) {
				return null;
			}

			if ($existReaction->is_liked === 0) {
				if ($isLiked === true) {
					$model->increment('like');
					$model->decrement('dislike');
				}
				if ($isLiked === -1) {
					$model->decrement('dislike');
					$model->increment('hesitant');
				}
			}
			if ($existReaction->is_liked === 1) {
				if ($isLiked === false) {
					$model->decrement('like');
					$model->increment('dislike');
				}
				if ($isLiked === -1) {
					$model->decrement('like');
					$model->increment('hesitant');
				}
			}
			if ($existReaction->is_liked === -1) {
				if ($isLiked === true) {
					$model->increment('like');
					$model->decrement('hesitant');

				}
				if ($isLiked === false) {
					$model->increment('dislike');
					$model->decrement('hesitant');
				}
			}

			$existReaction->is_liked = $isLiked;

			return $existReaction->save();
		}

		$likeModel = PetitionLikes::class;

		$like = new $likeModel([
			'is_liked' => $isLiked,
			'user_id'  => $this->getKey(),
		]);

		$model->likes()->save($like);
		if ($isLiked !== -1) {
			$model->increment($isLiked ? 'like' : 'dislike');
		} else {
			$model->increment('hesitant');
		}

		return $like;
	}

	/**
	 * @return HasMany
	 */
	public function petitionReactions()
	{
		return $this->hasMany(PetitionLikes::class);
	}

	/**
	 * @param Model $model
	 * @return mixed
	 */
	public function dislikePetition(Model $model)
	{
		return $this->likeOrDislikePetition($model, false);
	}

	/**
	 * @param Model $model
	 * @return mixed
	 */
	public function hesitantPetition(Model $model)
	{
		return $this->likeOrDislikePetition($model, -1);
	}

}
