<?php
namespace App\Traits;

use App\Interfaces\UserInterface;
use App\Models\UserAsset;

/**
 * Class HasAttachmentsTrait
 *
 * @package App\Traits
 * <AUTHOR> A<PERSON> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     UserInterface
 */
trait HasAttachmentsTrait
{
	/**
	 * @return \Illuminate\Database\Eloquent\Relations\MorphMany
	 */
	public function attachments()
	{
		return $this->morphMany(UserAsset::class, 'mediable')
			->orderBy('updated_at', 'DESC');
	}
}
