<?php

namespace App\Traits;

use Illuminate\Http\Request;

/**
 * Trait ResetsPasswords
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait ResetsPasswords
{
	use \Illuminate\Foundation\Auth\ResetsPasswords;

	/**
	 * Get the response for a successful password reset.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @param string                   $response
	 * @return \Dingo\Api\Http\Response
	 */
	protected function sendResetResponse(Request $request, $response)
	{
		return $this->response->noContent();
	}

	/**
	 * Get the response for a failed password reset.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @param string                   $response
	 * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
	 */
	protected function sendResetFailedResponse(Request $request, $response)
	{
		$this->response->error(trans($response), 422);
	}

	/**
	 * Get the password reset validation rules.
	 *
	 * @return array
	 */
	protected function rules()
	{
		return [
			'token'    => 'required',
			'email'    => 'required|email',
			'password' => 'required|confirmed|min:6',
		];
	}

}
