<?php
namespace App\Traits;

use App\Enums\GenderType;
use App\Enums\Roles;
use App\Interfaces\UserRepositoryInterface;
use Illuminate\Validation\Rule;

/**
 * Class UpdateUserTrait
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @property UserRepositoryInterface $userRepo
 */
trait UpdateUserTrait
{
	/**
	 * @param $request
	 * @return array
	 */
	public function validateUserData($request, $userId = null, $isAdmin = false)
	{
		$uniqueSkip = $userId ? ',' . $userId : '';

		$userValidators = [
			'first_name' => ['string', 'max:64', 'min:2'],
			'last_name'  => ['string', 'max:64', 'min:2'],
			'email'      => ['string', 'email', 'max:255', 'unique:users,email' . $uniqueSkip],
//			'username'   => ['nullable', 'string', 'min:3', 'alpha_dash', 'unique:users,username' . $uniqueSkip],
			'password'   => ['string', 'min:6', 'confirmed'],
			'picture_id' => ['nullable', 'int'],
		];
		if (!$userId) {
//			$userValidators['username'][] = 'required';
			$userValidators['email'][] = 'required';
		}
		if ($isAdmin) {
			$userValidators['role'] = ['array'];
			$roles                  = app(UserRepositoryInterface::class)
				->getAllRoles()->pluck('name')->toArray();

			$userValidators['role.*'] = [Rule::in($roles)];
		}

		//TODO add configurable detail columns
		$userDetailValidators = [
			'gender'      => Rule::in(GenderType::getMapKeys()),
			'birthday'    => ['date', 'date_format:Y-m-d'],
			'country'     => ['nullable', 'regex:/[A-Z]{2}/'],
			'timezone'    => ['timezone'],
			'description' => ['nullable', 'string', 'max:1024', 'min:2'],
			'language'    => ['nullable', 'regex:/[a-z]{2}/'],
		];

		$user       = $this->validate($userValidators, $request);
		$userDetail = $this->validate($userDetailValidators, (array)$request->details);

		return [$user, $userDetail];
	}

	/**
	 * @param $request
	 */
	protected function updateUserProcess($request, $userId, $admin = false)
	{
		list($user, $userDetail) = $this->validateUserData($request, $userId, $admin);

		$this->userRepo->updateUser($user, $userDetail);
	}

}
