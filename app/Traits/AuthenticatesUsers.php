<?php
	namespace App\Traits;

	use Illuminate\Foundation\Auth\RedirectsUsers;
	use Illuminate\Foundation\Auth\ThrottlesLogins;
	use Illuminate\Http\JsonResponse;
	use Illuminate\Http\Request;
	use Illuminate\Support\Facades\Auth;
	use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
	use JWTAuth;

	trait AuthenticatesUsers
	{
		use RedirectsUsers, ThrottlesLogins;
		use LoginResponseTrait, ValidateTrait;

		public function login(Request $request)
		{
			$this->validateLogin($request);

			if (method_exists($this, 'hasTooManyLoginAttempts') &&
				$this->hasTooManyLoginAttempts($request)) {
				$this->fireLockoutEvent($request);
				return $this->sendLockoutResponse($request);
			}

			if ($token = $this->attemptLogin($request)) {
				return $this->sendLoginResponse($request, $token);
			}

			$this->incrementLoginAttempts($request);
			return $this->sendFailedLoginResponse($request);
		}

		protected function validateLogin(Request $request)
		{
			// ValidateTrait'teki validate metodunu kullan
			$this->validate([
				$this->username() => 'required|string',
				'password'        => 'required|string',
			], $request);
		}

		/**
		 * Attempt to log the user into the application and return JWT token.
		 */
		protected function attemptLogin(Request $request)
		{
			$credentials = $this->credentials($request);

			if ($token = JWTAuth::attempt($credentials)) {
				return $token;
			}

			return false;
		}

		protected function credentials(Request $request): array
		{
			return $request->only($this->username(), 'password');
		}

		protected function sendLoginResponse(Request $request, $token): JsonResponse
		{
			$this->clearLoginAttempts($request);
			return $this->authenticated($request, auth()->user(), $token);
		}

		protected function authenticated(Request $request, $user, $token): JsonResponse
		{
			return response()->json($this->tokenResponse($token, $user));
		}

		protected function sendFailedLoginResponse(Request $request): void
		{
			throw new UnauthorizedHttpException('', "Bad Credentials");
		}

		public function username(): string
		{
			return 'email';
		}

		public function logout(Request $request): JsonResponse
		{
			JWTAuth::logout();
			return $this->loggedOut($request);
		}

		protected function loggedOut(Request $request): JsonResponse
		{
			return response()->json(['message' => 'Successfully logged out']);
		}

		protected function guard()
		{
			return Auth::guard('api');
		}
	}
