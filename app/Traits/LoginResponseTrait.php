<?php
	namespace App\Traits;

	trait LoginResponseTrait
	{
		/**
		 * Get the token array structure that should be returned for login/register.
		 */
		protected function tokenResponse($token, $user): array
		{
			return [
				'access_token' => $token,
				'token_type' => 'bearer',
				'expires_in' => auth('api')->factory()->getTTL() * 60,
				'user' => [
					'id' => $user->id,
					'first_name' => $user->first_name,
					'last_name' => $user->last_name,
					'email' => $user->email,
					'email_verified_at' => $user->email_verified_at,
					'created_at' => $user->created_at,
					'updated_at' => $user->updated_at,
				]
			];
		}
	}
