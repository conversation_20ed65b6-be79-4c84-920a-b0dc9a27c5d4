<?php
namespace App\Traits;

use App\Interfaces\UserInterface;
use App\Models\User;

/**
 * Class UserTrait
 *
 * @package App\Traits
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     UserInterface
 */
trait UserTrait
{
	/**
	 * @var User
	 */
	protected $user;

	/**
	 * @param User $user
	 * @return $this
	 */
	public function setUser(User $user): self
	{
		$this->user = $user;

		return $this;
	}

	/**
	 * @return User
	 */
	public function getUser(): ?User
	{
		return $this->user;
	}

}
