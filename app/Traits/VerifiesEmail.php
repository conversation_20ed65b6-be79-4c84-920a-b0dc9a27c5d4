<?php

namespace App\Traits;

use App\Interfaces\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;

/**
 * Trait VerifiesEmails
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait VerifiesEmail
{

	/**
	 * Mark the authenticated user's email address as verified.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function verify(Request $request, $userId)
	{
		/** @var User $user */
		$user = app(UserRepositoryInterface::class)->find($userId);
		if (!$user) {
			$this->response->errorUnauthorized();

			return;
		}

		if ($user->markEmailAsVerified()) {
			event(new Verified($user));
		}

		return $this->response->noContent();
	}

	/**
	 * Resend the email verification notification.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @return \Illuminate\Http\Response
	 */
	public function resend(Request $request)
	{
		$userRepo = app(UserRepositoryInterface::class);

		$this->validate([
			'email' => ['required', 'string', 'email', 'max:255'],
		], $request);

		$user = $userRepo->getUserByEmail($request->email);
		if (!$user) {
			return $this->response->noContent();
		}

		if ($user->hasVerifiedEmail()) {
			return $this->response->noContent();
		}

		$user->sendEmailVerificationNotification();

		return $this->response->noContent();
	}
}
