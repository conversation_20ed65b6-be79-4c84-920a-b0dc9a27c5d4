<?php
namespace App\Traits;

/**
 * Trait ValidationFields
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
Trait ValidationFields
{
	/**
	 * @param array|null $exclude
	 * @return array
	 */
	protected function getFields($validators = null, array $exclude = null)
	{
		$fields = array_keys($validators ?: $this->validators);
		if ($exclude) {
			return array_diff($fields, $exclude);
		}

		return $fields;
	}

	/**
	 * @param            $data
	 * @param array|null $exclude
	 * @return array
	 */
	protected function matchedFields($data, $validator = null, array $exclude = null)
	{
		return array_intersect_key($data, array_flip($this->getFields($validator, $exclude)));
	}
}
