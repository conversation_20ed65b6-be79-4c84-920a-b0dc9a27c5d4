<?php
namespace App\Traits;

/**
 * Class UserTrait
 *
 * @package App\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @see     FiltersInterface
 */
trait FiltersTrait
{

	protected $filters = [];

	/**
	 * @return array
	 */
	public function getFilters($key = null)
	{
		if ($key) {
			return isset($this->filters[$key]) ? $this->filters[$key] : null;
		}

		return $this->filters;
	}

	/**
	 * @param array $filters
	 * @return $this
	 */
	public function setFilters(array $filters)
	{
		$this->filters = $filters;

		return $this;
	}
}
