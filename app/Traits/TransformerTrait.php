<?php
namespace App\Traits;

use App\Transformers\GeneralTransformer;
use Dingo\Api\Http\Response;
use Dingo\Api\Http\Response\Factory;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Support\Collection;
use League\Fractal\TransformerAbstract;

/**
 * Class TransformerTrait
 * custom transformer can be specified as transformer property on main class
 * default is GeneralTransformer
 *
 * @package App\Traits
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @property string  $transformer
 * @property Factory $response
 */
trait TransformerTrait
{

	/**
	 * @param $modelOrCollection
	 * @return Response
	 */
	protected function transform($modelOrCollection, $params = [])
	{
		$transformerClass = isset($this->transformer) ? $this->transformer : GeneralTransformer::class;
		$transformer      = $transformerClass instanceof TransformerAbstract ? $transformerClass : new $transformerClass;

		if ($modelOrCollection instanceof Collection) {
			return $this->response->collection($modelOrCollection, $transformer, $params);
		}
		if ($modelOrCollection instanceof Paginator) {
			return $this->response->paginator($modelOrCollection, $transformer, $params);
		}

		if (!isset($modelOrCollection) || is_array($modelOrCollection)) {
			return $this->response->array((array)$modelOrCollection, $transformer, $params);
		}

		return $this->response->item($modelOrCollection, $transformer, $params);
	}
}
