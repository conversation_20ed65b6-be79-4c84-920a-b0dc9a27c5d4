<?php

namespace App\Traits;

use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

trait ImportFromFileTrait
{

	public function import()
	{
		$this->validate([
			'file_name' => 'required|file|max:3000'
		]);
		if(!$this->headingRowFormatter) {
			HeadingRowFormatter::default('none');
		}
		$col = Excel::toCollection($this->importClass, request()->file('file_name'));

		if (\request()->clearAll) {
			$this->repo->truncate();
		}

		return $this->repo->import($col);
	}

}
