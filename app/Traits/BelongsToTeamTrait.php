<?php
namespace App\Traits;

use App\Models\PetitionLikes;
use App\Models\Team;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Trait BelongsToTeamTrait
 *
 * @package Takdeniz\LikableComment\Traits
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
trait BelongsToTeamTrait
{

	/**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function team()
	{
		return $this->belongsTo(Team::class);
	}
}
