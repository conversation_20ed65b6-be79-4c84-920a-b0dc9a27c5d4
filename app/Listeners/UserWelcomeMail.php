<?php

namespace App\Listeners;

use App\Events\UserCreated;
use App\Notifications\WelcomeNotification;

/**
 * Class WatchedReward
 *
 * @package App\Listeners
 * <AUTHOR> A<PERSON>deniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UserWelcomeMail
{

	/**
	 * @param UserCreated $event
	 * @return mixed
	 */
	public function handle(UserCreated $event)
	{
		if (env('SENT_REGISTER_MAIL')) {
			$event->user->notify(new WelcomeNotification($event));
		}
	}
}
