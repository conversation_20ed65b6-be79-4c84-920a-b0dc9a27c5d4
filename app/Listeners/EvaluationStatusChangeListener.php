<?php

namespace App\Listeners;

use App\Events\EvaluationClosed;
use App\Helpers\Helper;
use App\Interfaces\EvaluationRepositoryInterface;
use App\Interfaces\PenaltyRepositoryInterface;
use App\Notifications\EvaluationStatusNotification;
use File;

/**
 * Class WatchedReward
 *
 * @package App\Listeners
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class EvaluationStatusChangeListener
{

	/**
	 * @param EvaluationClosed $event
	 */
	public function handle(EvaluationClosed $event)
	{
//		Helper::settings('sent_evaluation_mail',false)
		if (!env('SENT_EVALUATION_MAIL', false) ) {
			return;
		}

		$team    = $event->evaluation->team;
		$captain = $team->captain;
		if(!$captain){
			return;
		}

		$penaltyFile = storage_path('app/penalties/penalties_' . $team->team_name . '.xlsx');
		if (!File::exists(dirname($penaltyFile))) {
			File::makeDirectory(dirname($penaltyFile));
		}

		app(PenaltyRepositoryInterface::class)
			->export($event->evaluation->team->id, $penaltyFile);

		$evalFile = 'evaluations/evaluation_' . $team->team_name . '.xlsx';
		app(EvaluationRepositoryInterface::class)
			->setMainModel($event->evaluation)
			->export($evalFile);

		$captain->notify(new EvaluationStatusNotification($event, [
			$penaltyFile,
			storage_path('app/' . $evalFile),
		]));
	}
}
