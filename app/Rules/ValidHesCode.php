<?php

namespace App\Rules;

use App\Enums\HesCodeStatus;
use App\Models\Member;
use Illuminate\Contracts\Validation\Rule;

class ValidHesCode implements Rule
{
	private $value;
	/**
	 * @var bool
	 */
	private $identityMatch = true;

	/**
	 * Create a new rule instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		//
	}

	/**
	 * Determine if the validation rule passes.
	 *
	 * @param string $attribute
	 * @param Member $value
	 * @return bool
	 */
	public function passes($attribute, $value)
	{
		$this->value = $value;

		$result = isset($value['hes_code_result']) &&
			$value['hes_code_result']['status'] === HesCodeStatus::RISKLESS;

		if ($result) {
			return $this->identityMatch = $this->checkIdentity($value);
		}

		return $result;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		if (!$this->identityMatch) {
			return \Lang::get('validation.hes_code_not_match', [
				'name' => $this->value['first_name'] . ' ' . $this->value['last_name'],
			]);
		}

		return \Lang::get('validation.hes_code_not_valid', [
			'name'   => $this->value['first_name'] . ' ' . $this->value['last_name'],
			'status' => $this->value['hes_code_result'] ? $this->value['hes_code_result']['status'] : 'notexists'
		]);
	}

	protected function checkIdentity($value)
	{
		return substr($value['identity_number'], -3, 3)
			== substr($value['hes_code_result']['response']->masked_identity_number, -3, 3);
	}
}
