<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\ImplicitRule;

class RequiredMember implements ImplicitRule
{
	/**
	 * Create a new rule instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		//
	}

	/**
	 * Determine if the validation rule passes.
	 *
	 * @param string $attribute
	 * @param mixed  $value
	 * @return bool
	 */
	public function passes($attribute, $value)
	{
		return isset($value) && count($value) > 0;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return \Lang::get('validation.requiredMember');
	}
}
