<?php

namespace App\Providers;

use App\Transformers\CustomSerializer;
use Dingo\Api\Transformer\Adapter\Fractal;
use Dingo\Api\Transformer\Factory;
use Illuminate\Support\ServiceProvider;
use League\Fractal\Manager;
use Bouncer;
use App\Models\Team;

class AppServiceProvider extends ServiceProvider
{
	/**
	 * Register any application services.
	 *
	 * @return void
	 */
	public function register()
	{
		//
	}

	/**
	 * Bootstrap any application services.
	 *
	 * @return void
	 */
	public function boot()
	{
		app(Factory::class)->setAdapter(function ($app) {
			$fractal = new Manager;
			$fractal->setSerializer(new CustomSerializer());

			return new Fractal($fractal);
		});
		Bouncer::ownedVia(\App\Models\Team::class, function ($team, $user) {
			return $team->team_leader_email == $user->email;
		});

		Bouncer::ownedVia(\App\Models\Appointment::class, function ($appointment, $user) {
			return $user->email == $appointment->team->team_leader_email;
		});
	}
}
