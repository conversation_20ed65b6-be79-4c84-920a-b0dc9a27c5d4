<?php

namespace App\Providers;

use App\Events\EvaluationClosed;
use App\Events\UserCreated;
use App\Listeners\EvaluationStatusChangeListener;
use App\Listeners\UserWelcomeMail;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
	/**
	 * The event listener mappings for the application.
	 *
	 * @var array
	 */
	protected $listen = [
		Registered::class       => [
			SendEmailVerificationNotification::class,
		],
		UserCreated::class      => [
			UserWelcomeMail::class
		],
		EvaluationClosed::class => [
			EvaluationStatusChangeListener::class
		]
	];

	/**
	 * Register any events for your application.
	 *
	 * @return void
	 */
	public function boot()
	{
		parent::boot();
	}
}
