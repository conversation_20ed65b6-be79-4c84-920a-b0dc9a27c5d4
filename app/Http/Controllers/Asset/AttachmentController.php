<?php

namespace App\Http\Controllers\Asset;

use App\Http\Controllers\Controller;
use App\Interfaces\AssetRepositoryInterface;
use App\Traits\TransformerTrait;
use Illuminate\Http\Request;

/**
 * Class AttachmentController
 *
 * @package App\Http\Controllers\Asset
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AttachmentController extends Controller
{
	use TransformerTrait;

	/**
	 * @var AssetRepositoryInterface
	 */
	protected $repo;

	/**
	 * AssetController constructor.
	 *
	 * @param AssetRepositoryInterface $assetRepo
	 */
	public function __construct(AssetRepositoryInterface $assetRepo)
	{
		$this->repo = $assetRepo;
	}

	public function index()
	{
		return $this->transform($this->repo->systemFiles());
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Validation\ValidationException
	 */
	public function store(Request $request)
	{
		$this->validate([
			'asset_id' => 'required|exists:assets,id',
			'type'     => 'string'
		]);

		$file = $this->repo->addSystemFile($request->asset_id, $request->type);

		return $this->transform($file);
	}

	/**
	 * Display the specified resource.
	 *
	 * @param int $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$this->repo->deleteSystemFile($id);
	}

}
