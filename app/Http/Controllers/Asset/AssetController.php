<?php

namespace App\Http\Controllers\Asset;

use App\Http\Controllers\Controller;
use App\Interfaces\AssetRepositoryInterface;
use Illuminate\Http\Request;

/**
 * Class AssetController
 *
 * @package App\Http\Controllers\Asset
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AssetController extends Controller
{

	/**
	 * @var AssetRepositoryInterface
	 */
	protected $assetRepo;

	/**
	 * AssetController constructor.
	 *
	 * @param AssetRepositoryInterface $assetRepo
	 */
	public function __construct(AssetRepositoryInterface $assetRepo)
	{
		$this->assetRepo = $assetRepo;
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @param Request $request
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Validation\ValidationException
	 */
	public function store(Request $request)
	{
		$this->validate([
            'file' => 'required|mimes:doc,docx,pdf,xls,xlsx,jpeg,jpg,png,mp4,mov,heic,hevc|max:100000' // max:100MB
//			'file' => 'required|file|max:100000'  // max:100MB
		]);

		$asset = $this->assetRepo->setUser($this->user())->upload($request->file);

		return $this->response->array($asset->toArray()); //TODO added transformer
	}

	/**
	 * Display the specified resource.
	 *
	 * @param int $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
//		$this->authorize('destroy', Asset::class);
		$result = $this->assetRepo->setUser($this->user())->deleteAsset($id);

		return $this->response->array($result);
	}

}
