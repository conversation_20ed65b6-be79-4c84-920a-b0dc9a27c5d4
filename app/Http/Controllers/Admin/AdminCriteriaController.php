<?php
namespace App\Http\Controllers\Admin;

use App\Imports\CriteriaImport;
use App\Interfaces\CriteriaRepositoryInterface;
use App\Models\Criteria;
use Dingo\Api\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class AdminCriteriaController
 *
 * @package App\Http\Controllers\Admin
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AdminCriteriaController extends ResourceController
{
	protected $model = Criteria::class;

	/**
	 * AdminCriteriaController constructor.
	 *
	 * @param CriteriaRepositoryInterface $repo
	 */
	public function __construct(CriteriaRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$id = request()->route()->parameter('criterion');

		$validators = [
			'category'           => ['string'],
			'subject'            => ["string"],
			'sub_category'       => ["string"],
			'no'                 => ["string"],
			'type'               => ['integer', 'nullable'],
			'raw_rule'           => ['string', 'nullable'],
			'rule'               => ['string', 'nullable'],
			'content'            => ['string'],
			'after_race'         => ['boolean'],
			'active'             => ['nullable'],
			'is_required'        => ['boolean'],
			'allow_value'        => ['boolean'],
			'allow_disqualified' => ['required'],
			'allow_defective'    => ['required'],
			'allow_approve'      => ['required'],
		];

		if (!$id) {
			$validators['category'][]     = 'required';
			$validators['sub_category'][] = 'required';
			$validators['subject'][]      = 'required';
//			$validators['content'][]  = 'required';
		}

		return $validators;
	}

	public function multiCreate(Request $request)
	{
		$data = $request->json();
//		if (!$data->count()) {
//			throw new ResourceException('invalid payload');
//		}

		$this->saveMulti($data->all());
	}

	protected function saveMulti($data)
	{
		$rows = [];
		foreach ($data as $datum) {
			$rows[] = $this->validate(null, $datum);
		}

		foreach ($rows as $index => $row) {
			$rows[$index] = $this->repoCreate($row);
		}

		return $rows;
	}

	public function import()
	{
		$col = Excel::toCollection(new CriteriaImport(), request()->file('file_name'));

		if (\request()->clearAll) {
			$this->repo->truncate();
		}
		$tabs = $col->toArray();
		if (!\request()->importTabs) {
			$tabs = [$tabs[0]];
		}
		$criteriaCount = 0;
		$addedRows     = [];
		foreach ($tabs as $tab) {
			$rows = $this->repo->import($tab);
			$this->saveMulti($rows);
			$addedRows[]   = $rows;
			$criteriaCount += count($rows);
		}

		return [
			"criteriaCount" => $criteriaCount,
			"addedRows"     => $addedRows,
		];
	}

	/*
	 *
	 */
	protected function authFormat($base)
	{
		return 'admin' . parent::authFormat($base);
	}
}
