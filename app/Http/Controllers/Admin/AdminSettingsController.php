<?php
namespace App\Http\Controllers\Admin;

use App\Enums\Abilities;
use App\Enums\Roles;
use App\Interfaces\SettingsRepositoryInterface;
use App\Models\Settings;
use Bouncer;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;

/**
 * Class AdminSettingsController
 *
 * @package App\Http\Controllers\Admin
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AdminSettingsController extends ResourceController
{
	protected $model = Settings::class;

	/**
	 * AdminSettingsController constructor.
	 *
	 * @param SettingsRepositoryInterface $repo
	 */
	public function __construct(SettingsRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @param Request $request
	 * @return Response
	 * @throws AuthorizationException
	 * @throws ValidationException
	 */
	public function store(Request $request)
	{
		$this->authorize($this->authFormat(Abilities::CREATE), $this->model);

		$data  = $this->validate();
		$model = $this->repo->create($data);

		return $this->index();
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		return [
			'appointment_time'        => 'numeric',
			'appointment_slot_limit'  => 'numeric',
			'max_domestic_penalty'    => 'numeric',
			'team_max_appointment'    => 'numeric',
			'evaluation_start_date'   => 'date',
			'last_day_max_eval_count' => 'numeric',
			'promotions'              => 'array',
			'promotions.*'            => 'required|string',
		];
	}

	/*
	 *
	 */
	protected function authFormat($base)
	{
		return 'admin' . parent::authFormat($base);
	}

	public function clear()
	{
		$this->authorize($this->authFormat(Abilities::DELETE), $this->model);

		\Artisan::call('data:clear');
	}

	public function sync()
	{
//		Bouncer::allow(Roles::HES_CODE)->toManage(\App\Models\HesCode::class);
		Bouncer::allow(Roles::TEAM)->toManage([\App\Models\Member::class,]);
	}
}
