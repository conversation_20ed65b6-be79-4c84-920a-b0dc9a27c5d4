<?php
namespace App\Http\Controllers\Admin;

use App\Enums\Roles;
use App\Events\UserCreated;
use App\Http\Controllers\Controller;
use App\Imports\TechnicalDesignImport;
use App\Interfaces\UserRepositoryInterface;
use App\Models\User;
use App\Traits\TransformerTrait;
use App\Traits\UpdateUserTrait;
use App\Transformers\AdminUserTransformer;
use App\Transformers\GeneralTransformer;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class UserController
 *
 * @package App\Http\Controllers\Admin
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AdminUserController extends Controller
{
	use TransformerTrait;
	use UpdateUserTrait;

	protected $transformer = AdminUserTransformer::class;

	/**
	 * @var UserRepositoryInterface
	 */
	protected $userRepo;

	/**
	 * AdminUserController constructor.
	 *
	 * @param UserRepositoryInterface $userRepo
	 */
	public function __construct(UserRepositoryInterface $userRepo)
	{
		$this->userRepo = $userRepo;
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @return \Dingo\Api\Http\Response
	 * @throws AuthorizationException
	 */
	public function index()
	{
		$this->authorize('admin.list', User::class);

		$users = $this->userRepo->getAll();

		$users->load(['roles', 'details', 'picture']);

		return $this->transform($users);
	}

	/**
	 * @param Request $request
	 * @return Response
	 * @throws AuthorizationException
	 * @throws ValidationException
	 */
	public function store(Request $request)
	{
		$this->authorize('admin.create', User::class);

		list($userData, $detailData) = $this->validateUserData($request, null, true);

		$user = $this->userRepo->createUser($userData, $detailData);
		event(new UserCreated($user, $userData['password']));

		return $this->show($user->id);
	}

	/**
	 * @throws AuthorizationException
	 */
	public function show($userId)
	{
		$this->authorize('admin.show', User::class);

		$user = $this->userRepo->findOrFail($userId);

		return $this->transform($user);
	}

	/**
	 * @param Request $request
	 * @param         $userId
	 * @return \Dingo\Api\Http\Response
	 * @throws AuthorizationException
	 */
	public function update(Request $request, $userId)
	{
		$this->authorize('admin.update', User::class);

		$user = $this->userRepo->find($userId);

		$this->userRepo->setMainModel($user);

		$this->updateUserProcess($request, $userId, true);

		return $this->show($userId);
	}

	/**
	 * @param $userId
	 * @return mixed
	 * @throws AuthorizationException
	 */
	public function destroy($userId)
	{
		$this->authorize('admin.remove', User::class);

		$user = $this->userRepo->find($userId);

		$this->userRepo->removeUser($user);

		return $this->response->accepted();
	}

	public function importUsers()
	{
		$this->authorize('admin.update', User::class);

		$users = [];
		$col   = Excel::toCollection(new TechnicalDesignImport(), request()->file('file_name'));
		foreach ($col->first() as $line) {
			if (!$line->get('e_mail')) {
				continue;
			}
			$nameParse = explode(' ', $line->get('name'));

			$pass = ($line->get('password') && strlen($line->get('password') >= 6)) ? $line->get('password') : Str::random(8);

			$user    = [
				'first_name'            => trim(array_pop($nameParse)),
				'last_name'             => trim(implode(' ', $nameParse)),
				'email'                 => trim($line->get('e_mail')),
				'role'                  => array_map('trim', explode(',', $line['roles'])),
				'password'              => $pass,
				'password_confirmation' => $pass,
			];
			$users[] = $user;
			try {
				$this->store(new Request($user));
			} catch (ResourceException $e) {
				$users[] = $e->getErrors();
			}
		}

		return $this->response->array($users, new GeneralTransformer());
	}

	/**
	 * @return \Dingo\Api\Http\Response
	 * @throws AuthorizationException
	 * @throws ValidationException
	 */
	public function importDDK()
	{
		$this->authorize('admin.update', User::class);

		$users = [];
		$col   = Excel::toCollection(new TechnicalDesignImport(), request()->file('file_name'));
		foreach ($col->toArray()[0] as $item) {
			if (!isset($item['e_posta'])) {
				continue;
			}
			$nameParse = explode(' ', $item['ad_soyad']);
			$pass      = isset($item['password']) && strlen($item['password']) >= 6 ? $item['password'] :
				Str::random(12);
			$user      = [
				'first_name'            => trim(array_pop($nameParse)),
				'last_name'             => trim(implode(' ', $nameParse)),
				'email'                 => trim($item['e_posta']),
				'role'                  => array_map('trim', explode(',', $item['roles'])),
				'password'              => $pass,
				'password_confirmation' => $pass,
			];
			$users[]   = $user;
			try {
				$this->store(new Request($user));
			} catch (ResourceException $e) {
				$users[] = $e->getErrors();
			}
		}

		return $this->response->array($users, new GeneralTransformer());
	}

	public function updateDDK()
	{
		$this->authorize('admin.update', User::class);

		$users = [];
		$col   = Excel::toCollection(new TechnicalDesignImport(), request()->file('file_name'));
		$role  = null;
		foreach ($col->toArray()[0] as $item) {
			if (!isset($item['mail_bilgisi']) && !isset($item['e_posta'])) {
				continue;
			}
			$email = trim(isset($item['mail_bilgisi']) ? $item['mail_bilgisi'] : $item['e_posta']);
			if ($item['sekme']) { //kolon birlestirme icin
				$role = 'DDK ' . trim($item['sekme']);
			}

			if (isset($users[$email])) {
				$users[$email]['role'][] = $role;
				continue;
			}

			$nameParse = explode(' ', $item['ilgili_ddk']);
			$pass      = isset($item['password']) && strlen($item['password']) >= 6 ? $item['password'] :
				Str::random(8);
			$user      = [
				'first_name'            => trim(array_pop($nameParse)),
				'last_name'             => trim(implode(' ', $nameParse)),
				'email'                 => $email,
				'role'                  => [Roles::DDK, $role],
				'password'              => $pass,
				'password_confirmation' => $pass,
			];

			$users[$email] = $user;
		}
		foreach ($users as &$user) {
			try {
				if ($exists = $this->userRepo->getUserByEmail($user['email'])) {
					unset($user['password']);
					unset($user['password_confirmation']);

					$this->update(new Request($user), $exists->id);
					continue;
				}


				$this->store(new Request($user));
			} catch (ResourceException $e) {
				$user['error'] = $e->getErrors();
			}
		}

		return $this->response->array($users, new GeneralTransformer());
	}

	/**
	 * @return mixed
	 * @throws AuthorizationException
	 */
	public function getRoles()
	{
		$this->authorize('admin.list', User::class);

		return array('roles' => app(UserRepositoryInterface::class)->getAllRoles());
	}

	/**
	 * @return array
	 * @throws AuthorizationException
	 */
	public function listUserRoles()
	{
		$this->authorize('admin.list', User::class);

		$users = $this->userRepo->getAll();
		$roles = [];
		/** @var User $user */
		foreach ($users as $index => $user) {
			$roles[] = [
				'email' => $user->email,
				'roles' => $user->getRoles(),
			];
		}

		return $roles;
	}

	/**
	 * @param Request $request
	 * @return \Dingo\Api\Http\Response
	 * @throws AuthorizationException
	 */
	public function updateUserRoles(Request $request)
	{
		$this->authorize('admin.update', User::class);

		$users = $request->get('users');
		foreach ($users as $userRaw) {
			$user = $this->userRepo->getUserByEmail($userRaw['email']);
			$user->assign($userRaw['roles']);
		}

		return $this->response->noContent();
	}

}
