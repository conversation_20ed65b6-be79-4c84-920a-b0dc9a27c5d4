<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Abilities;
use App\Http\Controllers\Controller;
use App\Interfaces\BaseRepositoryInterface;
use App\Interfaces\FiltersInterface;
use App\Interfaces\Repository\GetAllPaginateInterface;
use App\Interfaces\Repository\MainModelMethodsInterface;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\TransformerTrait;
use App\Transformers\GeneralTransformer;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;

/**
 * Class ResourceController
 *
 * @package App\Http\Controllers\Admin
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class ResourceController extends Controller
{
	use TransformerTrait;
	protected $transformer   = GeneralTransformer::class;
	protected $listTransform = true;
	protected $loads         = [];
	/**
	 * @var BaseRepositoryInterface|MainModelMethodsInterface|FindOrFailMainModel
	 */
	protected $repo;

	protected $model;// = User::class;

	/**
	 * @return mixed
	 */
	abstract protected function validators();

	/**
	 * ResourceController constructor.
	 *
	 * @param BaseRepositoryInterface $repo
	 */
	public function __construct(BaseRepositoryInterface $repo)
	{
		$this->repo = $repo;
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @return Response
	 * @throws AuthorizationException
	 */
	public function index()
	{
		$this->authorize($this->authFormat(Abilities::LIST), $this->model);

		if ($this->repo instanceof FiltersInterface) {
			$this->repo->setFilters(request()->query->all());
		}

		$models = $this->repoGetAll();
		if (isset($this->loads['index'])) {
			$models->load($this->loads['index']);
		}

		if (!$this->listTransform) {
			$this->transformer = GeneralTransformer::class;
		}

		return $this->transform($models);
	}

	/**
	 * @param Request $request
	 * @return Response
	 * @throws AuthorizationException
	 * @throws ValidationException
	 */
	public function store(Request $request)
	{
		$this->authorize($this->authFormat(Abilities::CREATE), $this->model);

		$data  = $this->validate();
		$model = $this->repoCreate($data);

		return $this->show($model->id);
	}

	/**
	 * Display the specified resource.
	 *
	 * @param int $id
	 * @return Response
	 * @throws AuthorizationException
	 */
	public function show($id)
	{
		$model = $this->repoFind($id);
		if (isset($this->loads['show'])) {
			$model->load($this->loads['show']);
		}

		$this->authorize($this->authFormat(Abilities::VIEW), $model);

		return $this->transform($model);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param Request $request
	 * @param int     $id
	 * @return Response
	 * @throws AuthorizationException
	 */
	public function update(Request $request, $id)
	{
		$validatedData = $this->validate();

		$model = $this->repoFind($id);

		$this->authorize($this->authFormat(Abilities::UPDATE), $model);

		$this->repoUpdate($validatedData, $model);

		return $this->show($model->id);
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param int $id
	 * @return Response
	 * @throws AuthorizationException
	 */
	public function destroy($id)
	{
		$model = $this->repoFind($id);

		$this->authorize($this->authFormat(Abilities::DELETE), $model);

		$this->repoRemove($id);

		return $this->response->noContent();
	}

	/**
	 * @param $base
	 * @return string
	 */
	protected function authFormat($base)
	{
		return $base;
	}

	/**
	 * @return LengthAwarePaginator|Collection|Model[]
	 */
	protected function repoGetAll()
	{
		return $this->repo instanceof GetAllPaginateInterface ?
			$this->repo->getAllPaginate() : $this->repo->getAll();
	}

	/**
	 * @param $data
	 * @return Builder|Model
	 */
	protected function repoCreate($data)
	{
		return $this->repo->create($data);
	}

	/**
	 * @param $id
	 * @return Builder|Model|mixed|null
	 */
	protected function repoFind($id)
	{
		return $this->repo->findOrFail($id);
	}

	/**
	 * @param $id
	 * @return bool|mixed|null
	 */
	protected function repoRemove($id)
	{
		return $this->repo->remove($id);
	}

	/**
	 * @param      $data
	 * @param null $model
	 */
	protected function repoUpdate($data, $model = null)
	{
		$model && $this->repo->setMainModel($model);
		$this->repo->update($data);
	}
}
