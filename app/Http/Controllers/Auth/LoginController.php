<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Interfaces\UserRepositoryInterface;
use App\Traits\AuthenticatesUsers;

class LoginController extends Controller
{
	/*
	|--------------------------------------------------------------------------
	| Login Controller
	|--------------------------------------------------------------------------
	|
	| This controller handles authenticating users for the application and
	| redirecting them to your home screen. The controller uses a trait
	| to conveniently provide its functionality to your applications.
	|
	*/

	use AuthenticatesUsers;

	/**
	 * Create a new controller instance.
	 *
	 * @return void
	 */
	public function __construct(UserRepositoryInterface $userRepo)
	{
		$this->userRepo = $userRepo;
		$this->middleware('guest')->except('logout');
	}

	/**
	 * @param $token
	 * @param $user
	 * @return array
	 */
	public function tokenResponse($token, $user)
	{
		return [
			'token'      => $token,
			'token_type' => 'bearer',
			'expires_in' => auth()->factory()->getTTL() * 60,
			'user'       => [
				'id'                => $user->id,
				'profile_completed' => $user->profile_completed,
				'role'              => $user->role,
				'username'          => $user->username,
				'first_name'        => $user->first_name,
				'last_name'         => $user->last_name,
				'team'              => $user->team,
			],

		];
	}

}
