<?php

	namespace App\Http\Controllers\Auth;

	use App\Http\Controllers\Controller;
	use App\Interfaces\UserRepositoryInterface;
	use App\Models\User;
	use App\Traits\RegistersEmailUsers;


	/**
	 * Class RegisterController
	 *
	 * @package App\Http\Controllers\Auth
	 * <AUTHOR> <<EMAIL>>
	 * @version 0.1
	 * @since   0.1
	 */
	class RegisterController extends Controller
	{
		/*
		|--------------------------------------------------------------------------
		| Register Controller
		|--------------------------------------------------------------------------
		|
		| This controller handles the registration of new users as well as their
		| validation and creation. By default this controller uses a trait to
		| provide this functionality without requiring any additional code.
		|
		*/

		use RegistersEmailUsers;
//	use RegistersPhoneNumberUsers;
		private UserRepositoryInterface $userRepo;

		/**
		 * Create a new controller instance.
		 *
		 * @return void
		 */
		public function __construct(UserRepositoryInterface $userRepo)
		{
			$this->userRepo = $userRepo;
			$this->middleware('guest');
		}


		/**
		 * Create a new user instance after a valid registration.
		 *
		 * @param array $data
		 * @return User
		 */
		protected function create(array $data): User
		{
			return $this->userRepo->createUser($data);
		}
	}
