<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Traits\ValidateTrait;
use Dingo\Api\Routing\Helpers;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;

/**
 * Class Controller
 *
 * @package App\Http\Controllers
 * <AUTHOR> A<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @property User $user
 * @method User user()
 */
class Controller extends BaseController
{
	use AuthorizesRequests, DispatchesJobs; //, ValidatesRequests;

	use Helpers;
	use ValidateTrait;
}
