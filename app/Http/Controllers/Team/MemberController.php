<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Enums\GenderType;
use App\Enums\MemberType;
use App\Enums\Roles;
use App\Exports\MemberExport;
use App\Exports\MemberExportAll;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\MemberRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Member;
use App\Transformers\GeneralTransformer;
use Carbon\Carbon;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Validation\Rule;
use TCPDF;

/**
 * Class MemberController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @property MemberRepositoryInterface $repo
 */
class MemberController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Member::class;
	protected $loads       = [
//		'show' => 'files.asset'
	];

	/**
	 * MemberController constructor.
	 *
	 * @param MemberRepositoryInterface $repo
	 */
	public function __construct(MemberRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	protected function repoCreate($data)
	{
		$this->checkSameTeam($data);
		$this->checkMax($data);
		$this->checkUnique($data);

		return parent::repoCreate($data);
	}

	protected function repoUpdate($data, $model = null)
	{
		$model && $this->repo->setMainModel($model);

		$this->checkSameTeam($data);
		$this->checkUnique($data);

		parent::repoUpdate($data, $model);
	}

	protected function checkSameTeam($data)
	{
		if ($this->user->isA(Roles::TEAM_CAPTAIN)) {
			$team = app(TeamRepositoryInterface::class)->findTeamByCaptain($this->user->email);
			if ($team->id != $data['team_id']) {
				throw new ResourceException(\Lang::get('unauthorized.on.this.team'));
			}
		}
	}

	protected function checkUnique($data)
	{
		if (in_array($data['role_in_team'], [
				MemberType::Captain,
				MemberType::Driver,
				MemberType::ReserveDriver,
			]) && $this->repo->getMemberByRole($data['team_id'], $data['role_in_team']) > 0
		) {
			throw new ResourceException(\Lang::get('team.only_one_role', [
				'type' => \Lang::get('team.role_in_team.' . $data['role_in_team'])
			]));
		}
	}

	protected function checkMax($data)
	{
		$proms = app(MemberRepositoryInterface::class)->getTeamMembers($data['team_id']);

		if (count($proms) > 15) {
			throw new ResourceException(\Lang::get('team.max_member_count_exceeded'));
		}

//		if (count($proms) < 3) {
//			throw new ResourceException(\Lang::get('team.min_member_count_exceeded'));
//		}

	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$parentRequired = '';
		if (request()->birthday) {
			$date           = new Carbon(request()->birthday);
			$parentRequired = $date->gt('2004-07-19');
		}
		$id    = request()->route()->parameter('member');
		$model = $this->repo->find($id);
//
//		return [
//			'first_name'      => ['required', 'string',],
//			'last_name'       => ['required', 'string',],
//			'email'           => ['required', 'string', 'email'],
//			'team_id'         => ['required', 'integer', 'exists:teams,id',],
//			'role_in_team'    => ['required', 'string', Rule::in(MemberType::getValues())],
//			'identity_number' => ['required', 'string', request()->not_tc_citizen ? '' : 'tckimlik', 'unique:members,identity_number' . ($id ? ',' . $id : '')],
//			'not_tc_citizen'  => ['boolean'],
//			'phone_number'    => ['required', 'string', 'min:10'],
//			'birthday'        => ['required', 'date',],
//			'gender'          => ['required', 'string', 'nullable', Rule::in(GenderType::getMapKeys())],
//			'in_area'         => ['boolean', 'nullable'],
//			'parent_name'     => [new RequiredIf($parentRequired),],
//			'parent_phone'    => [new RequiredIf($parentRequired),],
//			'uniform_size'    => ['required', 'string',],
////			'hes_code'        => ['required', 'string', 'size:10'],
//			'picture_id'      => [new RequiredIf(!$model || !$model->picture_url), 'nullable'],
//		];
//

		// TODO kayit icin gecici
		return [
			'first_name'      => ['required', 'string',],
			'last_name'       => ['required', 'string',],
			'email'           => ['required', 'string', 'email'],
			'team_id'         => ['required', 'integer', 'exists:teams,id',],
			'role_in_team'    => ['required', 'string', Rule::in(MemberType::getValues())],
			'identity_number' => ['required', 'string', request()->not_tc_citizen ? '' : 'tckimlik', 'unique:members,identity_number' . ($id ? ',' . $id : '')],
			'not_tc_citizen'  => ['boolean'],
			'phone_number'    => ['required', 'string', 'min:10'],
			'birthday'        => ['date',],
			'gender'          => ['string', 'nullable', Rule::in(GenderType::getMapKeys())],
			'in_area'         => ['nullable'],
			'parent_name'     => ['nullable'],
			'parent_phone'    => ['nullable'],
			'uniform_size'    => ['nullable',],
//			'hes_code'        => ['required', 'string', 'size:10'],
			'picture_id'      => ['nullable'],
		];
	}

	/**
	 * @return \Maatwebsite\Excel\BinaryFileResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function export($teamId)
	{
		$this->authorize(Abilities::LIST, $this->model);

		return (new MemberExport($teamId))->download('Tubitak_EC_Members.xlsx');
	}

	/**
	 * @return \Maatwebsite\Excel\BinaryFileResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function exportAll()
	{
		$this->authorize(Abilities::LIST, $this->model);

		return (new MemberExportAll())->download('Tubitak_EC_Members.xlsx');
	}

	/**
	 * @param $id
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function makeQRCode($id)
	{
		$this->authorize('makeQRCode', $this->model);

		$this->repo->findMainModelOrFail($id)->makeQRCode();

		return $this->show($id);
	}

	/**
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function makeQrCodeForAllMembers()
	{
		$this->authorize('makeQRCode', $this->model);

		$this->repo->makeQrCodeForAllMembers();

		return $this->response->noContent();
	}

	public function makeTeamQRCodePdf($teamId)
	{
		$this->authorize('makeQRCode', $this->model);

		$width      = 60;
		$height     = 60;
		$pageLayout = array($width, $height);
		$pdf        = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, $pageLayout, true, 'UTF-8', false);

		$pdf->SetAutoPageBreak(false, 0);

		$members = $this->repo->getTeamMembers($teamId);
		foreach ($members as $member) {
			if (!$member->qr_code) {
				$member->qr_code = $this->repo->setMainModel($member)->makeQRCode();
//				continue;
			}
			if (!$member->in_area) {
				continue;
			}

			$pdf->AddPage();
			$pdf->Image(storage_path('app/public' . $member->qr_code), 5, 5, 50, 50, '', '', '', false, 300, '', false, false, 0);
		}

		$fileName = storage_path('app/qr_codes_' . $teamId . '.pdf');
		$pdf->Output($fileName, 'F');

//		return response()->file($pdf->Output(storage_path('example_001.pdf'), 'I'));
		return response()->file($fileName);
	}

}
