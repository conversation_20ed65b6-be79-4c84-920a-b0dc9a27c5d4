<?php

	namespace App\Http\Controllers\Team;

	use App\Http\Controllers\Controller;
	use App\Interfaces\TeamRepositoryInterface;
	use App\Interfaces\UserRepositoryInterface;
	use App\Transformers\GeneralTransformer;
	use App\Transformers\TeamTransformer;


	use Illuminate\Http\Request;
	use Illuminate\Http\Response;
	use Illuminate\Validation\ValidationException;
	use Nette\NotImplementedException;
	use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

	/**
	 * Class MyTeamController
	 *
	 * @package App\Http\Controllers\User
	 * <AUTHOR> Akdeniz <<EMAIL>>
	 * @version 0.1
	 * @since   0.1
	 */
	class MyTeamController extends Controller
	{
		/**w
		 *
		 * @var UserRepositoryInterface
		 */
		protected $teamRepo;

		/**
		 * UserController constructor.
		 *
		 * @param TeamRepositoryInterface $userRepo
		 */
		public function __construct(TeamRepositoryInterface $userRepo)
		{
			$this->teamRepo = $userRepo;
			$this->middleware(function ($request, $next) {
				if ($this->auth->getUser()->team) {
					$this->teamRepo->setMainModel($this->auth->getUser()->team);
				}

				return $next($request);
			});
		}

		/**
		 * Display a listing of the resource.
		 *
		 * @return Response
		 */
		public function show(): Response
		{
			$team = $this->getTeam();

			$team->load(['files.asset', 'appointments', 'members', 'forms.attachments.asset']);

			return $this->response->item($team, new TeamTransformer());
		}

		protected function validators()
		{
			$id = $this->user()->team->id;

			return [
				'team_name' => ["string", "min:4", "unique_with:teams,team_name,vehicle_name" . ($id ? ',' . $id : '')],
			];
		}

		/**
		 * Update the specified resource in storage.
		 *
		 * @param Request $request
		 * @return Response
		 * @throws ValidationException
		 */
		public function update(Request $request)
		{
			$team = $this->getTeam();
			$data = $this->validate();
			$this->teamRepo->update($data);

			return $this->show();
		}

		/**
		 * @return mixed
		 */
		protected function getTeam()
		{
			$team = $this->user()->team;
			if (!$team) {
				throw new NotFoundHttpException(\Lang::get('team.not_found'));
			}

			return $team;
		}

		public function checkMyTeam(Request $request): \Dingo\Api\Http\Response
		{
			$errors = $this->teamRepo->setMainModel($this->getTeam())
				->checkTeam();

			return $this->response->array($errors, new GeneralTransformer());
		}

		/**
		 * @param Request $request
		 * @return Response
		 */
		public function vehicleNumber(Request $request): Response
		{
			$team = $this->getTeam();
			$this->teamRepo->setMainModel($team)->giveVehicleNumber();

			return $this->show();
		}

		public function destroy(Request $request): void
		{
			// TODO: Implement destroy() method.
			throw new NotImplementedException(\Lang::get('team.not_implemented'));
		}

	}
