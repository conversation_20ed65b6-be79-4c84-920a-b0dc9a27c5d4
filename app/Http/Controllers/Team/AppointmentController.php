<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Enums\AppointmentStatusType;
use App\Exports\AppointmentExport;
use App\Exports\TeamExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\AppointmentRepositoryInterface;
use App\Interfaces\EvaluationRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Appointment;
use App\Models\Team;
use App\Transformers\GeneralTransformer;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;

//use Dingo\Api\Contract\Http\Request;

/**
 * Class AppointmentController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AppointmentController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Appointment::class;
	protected $loads       = [
		'show'  => 'team.currentEvaluation',
		'index' => 'team.currentEvaluation',
	];

	/**
	 * AppointmentController constructor.
	 *
	 * @param AppointmentRepositoryInterface $repo
	 */
	public function __construct(AppointmentRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	public function repoGetAll()
	{
		request()->query->set('limit', 1000);

		return parent::repoGetAll(); // TODO: remove it later
	}


	public function import()
	{
		$this->authorize(Abilities::IMPORT, $this->model);
		$this->validate([
			'file_name' => 'required|file|max:3000'
		]);

		$col = Excel::toCollection(new Appointment, request()->file('file_name'));

		return $this->repo->import($col);
	}

	public function export()
	{
		header("Cache-Control: no-cache, must-revalidate"); //HTTP 1.1

		return (new AppointmentExport())->download( 'appointment.xlsx');
	}


	/**
	 * @return array
	 *
	 * @ hbt1903
	 * New validation is added (protects status to be changed by team captain)
	 */
	protected function validators()
	{
		$id         = request()->route()->parameter('appointment');
		$isCaptain  = $this->user->isAn(\App\Enums\Roles::TEAM_CAPTAIN);
		$validators = [
			'start_time' => ['date'],
			'status'     => [Rule::in(AppointmentStatusType::getValues())],
//			'joined'     => ['boolean'],
			'comments'   => ['nullable', 'string'],
		];

		if (!$id) {
			$validators['team_id'] = ['integer', 'exists:teams,id', 'required'];

			$validators['start_time'][] = 'required';
		}

		if ($isCaptain) {
			unset($validators["status"]);
		}

		return $validators;
	}

	/**
	 * @param $data
	 * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model
	 */
	protected function repoCreate($data)
	{
		// @hbt1903
		// Add extra controls for the scenario that team captain is creating appointment
		$userId    = $this->user()->id;
		$userEmail = $this->user()->email;
		$isCaptain = $this->user->isAn(\App\Enums\Roles::TEAM_CAPTAIN);

		if ($isCaptain) {
			$team = app(TeamRepositoryInterface::class)->findTeamByCaptain($userEmail);

			$data['team_id'] = $team->id;
			$countOnSameTime = Appointment::where("start_time", $data["start_time"])->count();
			if ($countOnSameTime > 2) {
				throw new ResourceException(\Lang::get('You can not set an appointment at this time because all 3 slots are full'));
			}
		}
		$data['created_by'] = $userId;

		return parent::repoCreate($data);
	}

	public function join(Request $request, $id)
	{
		$this->authorize(Abilities::UPDATE, $this->model);

		$data = $this->validate([
			'joined' => 'bool'
		]);
		$this->repo->findMainModelOrFail($id);

		$this->repo->joined($request->joined);
	}

	public function update(Request $request, $id)
	{
		$isCaptain = $this->user->isAn(\App\Enums\Roles::TEAM_CAPTAIN);
		if ($isCaptain && isset($request->start_time)) {
			$timeBefore      = Appointment::get($id)->start_time;
			$countOnSameTime = Appointment::where("start_time", $request->start_time)->count();
			if ($countOnSameTime > 2 && $timeBefore != $request->start_time) {
				throw new ResourceException(\Lang::get('You can not set an appointment at this time because all 3 slots are full'));
			}
		}

		$model = $this->repo->findOrFail($id);

		// appoinment not joined olarak isareteniyorsa
		if ($request->status == AppointmentStatusType::NOT_JOINED
			&& $model->status !== AppointmentStatusType::NOT_JOINED) {
			app(EvaluationRepositoryInterface::class)
				->appointmentRejectOperation($model->team_id);

		}

		return parent::update($request, $id);
	}

	/**
	 * @param Request $request
	 * @return \Dingo\Api\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function autoAppointment(Request $request)
	{
		$this->authorize(Abilities::CREATE, $this->model);

		$this->validate([
			'start_time' => 'date'
		]);

		$result = $this->repo->autoAppointment($request->start_time);

		return $this->transform((object)$result);
	}
}
