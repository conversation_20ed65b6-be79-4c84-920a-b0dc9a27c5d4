<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\PromotionRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Promotion;
use App\Transformers\GeneralTransformer;

/**
 * Class PromotionController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class PromotionController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Promotion::class;
	protected $loads       = [
//		'show' => 'files.asset'
	];

	/**
	 * PromotionsController constructor.
	 *
	 * @param PromotionRepositoryInterface $repo
	 */
	public function __construct(PromotionRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
//		$id = request()->route()->parameter('promotion');

		return [
			'name'     => "required|string",
			'team_id'  => 'integer|exists:teams,id',
			'quantity' => 'numeric',
		];
	}

	public function multiPromotion()
	{
		$data = $this->validate([
			'name'    => 'array',
			'name.*'  => 'required|string',
			'team_id' => 'required|integer|exists:teams,id',
		]);

		$this->repo->multiInsert($data);
	}

	/**
	 * @param $teamId
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function export($teamId)
	{
		$this->authorize($this->authFormat(Abilities::LIST), $this->model);

		return $this->repo->export($teamId);
	}

	/**
	 * @param $teamId
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function exportAll()
	{
		$this->authorize($this->authFormat(Abilities::LIST), $this->model);

		return $this->repo->exportAllTeamsPromotions();
	}

	public function resetPromotions()
	{
		$this->authorize($this->authFormat(Abilities::DELETE), $this->model);

		\DB::statement('TRUNCATE TABLE promotions');

		foreach (app(TeamRepositoryInterface::class)->getAll() as $team) {
			$this->repo->generatePromotionForTeam($team->id);
		}
	}

}
