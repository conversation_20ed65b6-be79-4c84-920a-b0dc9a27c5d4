<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Http\Controllers\Controller;
use App\Interfaces\HesCodeServiceInterface;
use App\Interfaces\TechnicalDesignRepositoryInterface;
use App\Models\HesCode;
use App\Traits\TransformerTrait;
use App\Transformers\GeneralTransformer;
use Illuminate\Support\Collection;

/**
 * Class HesCodeController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class HesCodeController extends Controller
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = HesCode::class;

	use TransformerTrait;

	/**
	 * @var HesCodeServiceInterface
	 */
	private $hesCodeService;

	/**
	 * DomesticPartsController constructor.
	 *
	 * @param TechnicalDesignRepositoryInterface $repo
	 */
	public function __construct(HesCodeServiceInterface $hesCodeService)
	{
		$this->hesCodeService = $hesCodeService;
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		return [
			'hes_code' => 'required|string|min:10|max:12',
		];
	}

	public function checkHesCode()
	{
		$this->authorize(Abilities::CHECK_HES_CODE, $this->model);

		$data = $this->validate();

		$res = $this->hesCodeService->checkHesCode($data['hes_code']);

		return $this->transform($res);
	}

	public function checkHesCodes()
	{
		$this->authorize(Abilities::CHECK_HES_CODE, $this->model);

		$data = $this->validate([
			'hes_codes.*' => 'required|string|min:10|max:12',
		]);

		$res = $this->hesCodeService->checkHesCodes($data['hes_codes']);

		return $this->transform(new Collection($res));
	}

}
