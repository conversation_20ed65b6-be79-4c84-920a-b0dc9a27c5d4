<?php
namespace App\Http\Controllers\Team;

use App\Enums\EvaluationAbilities;
use App\Enums\PenaltyType;
use App\Exports\PenaltiesExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Imports\PenaltyImport;
use App\Interfaces\PenaltyRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Penalty;
use App\Traits\ImportFromFileTrait;
use App\Transformers\GeneralTransformer;
use Illuminate\Validation\Rule;

/**
 * Class PenaltyController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class PenaltyController extends ResourceController
{
	use ImportFromFileTrait;

	protected $transformer = GeneralTransformer::class;
	protected $model       = Penalty::class;
	protected $loads       = [
//		'show' => 'files.asset'
	];

	/**
	 * @var PenaltyImport
	 */
	protected $importClass;
	protected $headingRowFormatter = true;
	/**
	 * PenaltyController constructor.
	 *
	 * @param PenaltyRepositoryInterface $repo
	 */
	public function __construct(PenaltyRepositoryInterface $repo)
	{
		parent::__construct($repo);
		$this->importClass = new Penalty();
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
//		$id = request()->route()->parameter('promotion');

		return [
			'team_id'    => 'integer|exists:teams,id',
			'session_id' => 'integer|nullable|exists:sessions,id',
			'subject'    => 'string',
			'violation'  => 'string',
			'penalty'    => 'required|numeric',
			'type'       => ['string', 'nullable', Rule::in(PenaltyType::getValues())],
			'conclusion' => 'string|nullable|max:255',
		];
	}

	/**
	 * @param $teamId
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function export($teamId)
	{
		$this->authorize(EvaluationAbilities::LIST, $this->model);
		$team = app(TeamRepositoryInterface::class)->findOrFail($teamId);

		$filename = 'penalty_' . $team->team_name . '.xlsx';
//		$writer->save('/Users/<USER>/test.xlsx');

		// Redirect output to a client’s web browser (Xlsx)
		header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		header('Content-Disposition: attachment;filename="' . $filename . '"');
		header('Cache-Control: max-age=0');
		header('Access-Control-Allow-Origin:*');

		$writer = $this->repo->export($teamId, 'php://output');
	}

	/**
	 * @return \Maatwebsite\Excel\BinaryFileResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function exportAll()
	{
		return (new PenaltiesExport())->download('Tubitak_EC_Penalties.xlsx');
	}
}
