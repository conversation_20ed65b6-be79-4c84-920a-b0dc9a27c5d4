<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\PetitionRepositoryInterface;
use App\Models\Petition;
use App\Transformers\GeneralTransformer;

/**
 *
 */
class PetitionController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Petition::class;
	protected $loads       = [
		'show'  => [
			'attachments.asset',
			'comments.commented',
			'team',
			'likes.user',
			'session.race'
		],
		'index' => ['team', 'session.race'],
	];

	/**
	 * FormController constructor.
	 *
	 * @param PetitionRepositoryInterface $repo
	 */
	public function __construct(PetitionRepositoryInterface $repo)
	{
		parent::__construct($repo);
		$this->middleware(function ($request, $next) {
			$this->repo->setUser($this->auth->getUser());

			return $next($request);
		});
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function like($id)
	{
		$this->authorize(Abilities::LIKE, $this->model);

		$model = $this->repo->findOrFail($id);

		$this->repo->setMainModel($model)->like();

		return $this->transform($model);
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function dislike($id)
	{
		$this->authorize(Abilities::LIKE, $this->model);

		$model = $this->repo->findOrFail($id);
		$this->repo->setMainModel($model)->dislike();

		return $this->transform($model);
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function hesitant($id)
	{
		$this->authorize(Abilities::LIKE, $this->model);

		$model = $this->repo->findOrFail($id);
		$this->repo->setMainModel($model)->hesitant();

		return $this->transform($model);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		return [
			'team_id'                => 'required',
			'content'                => 'string',
			'type'                   => 'string',
			'target'                 => 'string',
			'status'                 => 'string',
//			'session_id'             => 'required|exists:sessions,id',
			'session_name'           => 'string',
			'active'                 => 'integer',
			'attachments.*.asset_id' => 'exists:assets,id',
			'attachments.*.type'     => 'string'
		];
	}

}
