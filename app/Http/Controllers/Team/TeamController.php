<?php

namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Enums\VehicleCategory;
use App\Exports\TeamExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Team;
use App\Transformers\GeneralTransformer;
use App\Transformers\TeamTransformer;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class TeamController
 *
 * @package App\Http\Controllers\Admin
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class TeamController extends ResourceController
{
	protected $transformer = TeamTransformer::class;
	protected $model = Team::class;
	protected $loads = [
		'show' => [
			'files.asset', 'appointments', 'promotions', 'penalties',
//			'members', 'members.hesCodeResult', 'forms.attachments.asset'
			'members', 'forms.attachments.asset',
		],
	];

	/**
	 * TeamController constructor.
	 *
	 * @param TeamRepositoryInterface $repo
	 */
	public function __construct(TeamRepositoryInterface $repo)
	{
//		\DB::connection()->enableQueryLog();
		parent::__construct($repo);
	}

	/**
	 * @return array
	 * @hbt1903
	 * New validation for captain role is added (protects against changing captain email)
	 */
	protected function validators()
	{
		$id = request()->route()->parameter('team');
		$isCaptain = $this->user->isAn(\App\Enums\Roles::TEAM_CAPTAIN);

		$validators = [
			'team_name'           => ["string", "min:2", "unique_with:teams,team_name,vehicle_name" . ($id ? ',' . $id : '')],
			'university_name'     => 'string|max:255',
			'vehicle_name'        => 'string|max:255',
			'city_name'           => 'string|max:255',
			//'team_leader'         => 'string|max:255',
			//'team_leader_phone'   => 'string|max:255',
			//'team_leader_email'   => 'string|max:255|email',
			//'consultant_name'     => 'string|max:255',
			//'consultant_phone'    => 'string|max:255',
			//'consultant_email'    => 'string|max:255|email',
			//'curator_name'        => 'string|max:255',
			//'curator_phone'       => 'string|max:255',
			//'curator_email'       => 'string|max:255|email',
			//'driver_name'         => 'string|max:255',
			//'driver_phone'        => 'string|max:255',
			//'driver_email'        => 'string|max:255|email',
			//'second_driver_name'  => 'string|max:255',
			//'second_driver_phone' => 'string|max:255',
			//'second_driver_email' => 'string|max:255|email',
			'vehicle_category'    => ['string', 'max:255', 'nullable', Rule::in(VehicleCategory::getValues())],
			'vehicle_number'      => 'integer|min:1|max:99|unique:teams,vehicle_number' . ($id ? ',' . $id : ''),
			'team_member_count'   => 'integer',
			//'logo_id'             => 'integer|exists:assets,id',
		];

		if (!$id) {
			$validators['team_name'][] = 'required';
		}

		/*
		if ($isCaptain) {
			unset($validators["team_leader_email"]);
		}
		*/

		return $validators;
	}

	public function approve($id)
	{
		$this->authorize(Abilities::APPROVE, $this->model);

		return $this->repo->findMainModelOrFail($id)->approve();
	}

	public function approveAllPossibleTeams()
	{
		return $this->repo->approveAllPossibleTeams();
	}

	/**
	 * @return mixed
	 */
	public function export()
	{
		header("Cache-Control: no-cache, must-revalidate"); //HTTP 1.1

		//$this->authorize('export', $this->model);
		return (new TeamExport())->download( 'teams.xlsx');
	}

	public function import()
	{
		$this->authorize(Abilities::IMPORT, $this->model);
		$this->validate([
			'file_name' => 'required|file|max:3000'
		]);

		$col = Excel::toCollection(new Team, request()->file('file_name'));

		return $this->repo->import($col);
	}

	/**
	 * @param Request $request
	 * @param         $id
	 */
	public function attach(Request $request, $id)
	{
		$this->authorize(Abilities::UPDATE, $this->model);

		$this->validate([
			'asset_id' => 'required|exists:assets,id',
			'type'     => 'string'
		]);

		$this->repo->findMainModelOrFail($id);
		$this->repo->attachFile($request->asset_id, $request->type);
	}

	public function checkTeamHesCodes($id)
	{
		$this->authorize(Abilities::CHECK_HES_CODE, $this->model);

		$this->repo->findMainModelOrFail($id);

		$this->repo->checkTeamHesCodes();

		return $this->show($id);
	}

	public function getTeamsFromKYS()
	{
		\Artisan::call('import:team');

		return \Artisan::output();
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function vehicleNumber(Request $request, $id)
	{
		$this->authorize(Abilities::UPDATE, $this->model);

		$this->repo->findMainModelOrFail($id)
			->giveVehicleNumber();

		return $this->show($id);
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return mixed
	 */
	public function checkTeam(Request $request, $id){
		$this->authorize(Abilities::LIST, $this->model);

		$errors = $this->repo->findMainModelOrFail($id)->checkTeam();

		return $this->response->array($errors, new GeneralTransformer());
	}

	/**
	 * @param Request $request
	 * @return \Dingo\Api\Http\Response
	 */
	public function giveVehicleNumberForAllTeams(Request $request)
	{
		$this->authorize(Abilities::UPDATE, $this->model);

		$teams= $this->repo->giveVehicleNumberForAllTeams();

		return $this->transform($teams);
	}




}
