<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Http\Controllers\Controller;
use App\Interfaces\CommentRepositoryInterface;
use App\Interfaces\PetitionRepositoryInterface;
use App\Models\Petition;
use App\Traits\TransformerTrait;
use App\Transformers\CommentsTransformer;
use Dingo\Api\Contract\Http\Request;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Response;

/**
 * Class CommentController
 *
 * @package App\Http\Controllers\watch
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CommentController extends Controller
{
	use TransformerTrait;

	protected $model = Petition::class; // important

	protected $transformer = CommentsTransformer::class;
	/**
	 * @var CommentRepositoryInterface
	 */
	protected $repo;
	/**
	 * @var PetitionRepositoryInterface
	 */
	protected $topicRepo;

	public function __construct(CommentRepositoryInterface $commentRepo, PetitionRepositoryInterface $topicRepo)
	{
		$this->repo      = $commentRepo;
		$this->topicRepo = $topicRepo;
		$this->middleware(function ($request, $next) {
			$this->repo->setUser($this->auth->getUser());

			return $next($request);
		});
	}

	/**
	 * @param         $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function getComments($id)
	{
		$results = $this->repo->getComments($this->findTopic($id));

		return $this->transform($results);
	}

	/**
	 * @param $id
	 * @return Petition
	 */
	protected function findTopic($id)
	{
		return $this->topicRepo->findOrFail($id);
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function comment(Request $request, $id)
	{
		$this->authorize(Abilities::LIKE, $this->model);

		$this->validate([
			'comment' => 'required|string|max:500'
		]);

		$topic = $this->findTopic($id);

//		if (!$app->userChoice) {
//			throw new ResourceException("vote.first");
//		}

		$comment = $this->repo->comment($topic, $request->comment);

		return $this->transform($comment);
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function like($id)
	{
		$this->authorize(Abilities::LIKE, $this->model);

		$comment = $this->repo->findOrFail($id);

		$this->repo->setMainModel($comment)->like();

		return $this->transform($comment);
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function dislike($id)
	{
		$this->authorize(Abilities::LIKE, $this->model);

		$comment = $this->repo->findOrFail($id);
		$this->repo->setMainModel($comment)->dislike();

		return $this->transform($comment);
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param int $id
	 * @return Response
	 * @throws AuthorizationException
	 */
	public function destroy($id)
	{
		$model = $this->repo->findMineOrFail($id);

		$this->authorize(Abilities::DELETE, $model);

		$this->repo->setMainModel($model)->delete();

		return $this->response->noContent();
	}

}
