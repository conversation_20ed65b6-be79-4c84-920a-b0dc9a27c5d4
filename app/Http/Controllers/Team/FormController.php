<?php
namespace App\Http\Controllers\Team;

use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\FormRepositoryInterface;
use App\Models\Form;
use App\Transformers\GeneralTransformer;

/**
 * Class FormController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class FormController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Form::class;
	protected $loads       = [
		'show'  => 'attachments.asset',
		'index' => 'attachments.asset'
	];

	/**
	 * FormController constructor.
	 *
	 * @param FormRepositoryInterface $repo
	 */
	public function __construct(FormRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		return [
			'team_id'                => 'required',
			'name'                   => 'required|string',
			'surname'                => 'required|string',
			'description'            => 'string',
			'attachments.*.asset_id' => 'exists:assets,id',
			'attachments.*.type'     => 'string'
		];
	}

}
