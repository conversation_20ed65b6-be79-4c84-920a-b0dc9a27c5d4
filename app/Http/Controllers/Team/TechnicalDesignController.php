<?php
namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Exports\TechnicalDesignExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Imports\TechnicalDesignImport;
use App\Interfaces\TechnicalDesignRepositoryInterface;
use App\Models\TechnicalDesign;
use App\Transformers\GeneralTransformer;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rules\RequiredIf;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

/**
 * Class TechnicalDesignController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class TechnicalDesignController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = TechnicalDesign::class;
	protected $loads       = [
		'show'  => 'team',
		'index' => 'team'
	];

	/**
	 * DomesticPartsController constructor.
	 *
	 * @param TechnicalDesignRepositoryInterface $repo
	 */
	public function __construct(TechnicalDesignRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$id = request()->route()->parameter('technical_design');

		return [
			'team_id'        => [new RequiredIf(!$id), 'integer'],
			'team_no'        => 'integer',
			'key'            => 'string',
			'point'          => 'integer',
			'original_point' => 'integer',
			'copy'           => 'boolean',
			'domestic'       => 'boolean',
			'comment'        => 'string',

		];
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param Request $request
	 * @param int     $id
	 * @return Response
	 * @throws AuthorizationException
	 */
	public function update(Request $request, $id)
	{
		parent::update($request, $id);

		return $this->listByTeam($request->team_id);
	}

	/**
	 * @param $teamId
	 * @return \Dingo\Api\Http\Response
	 * @throws AuthorizationException
	 */
	public function listByTeam($teamId)
	{
		$this->authorize($this->authFormat(Abilities::LIST), $this->model);
		$result = $this->repo->getByTeam($teamId);

		return $this->transform($result);
	}

	public function import()
	{
		$this->validate([
			'file_name' => 'required|file|max:3000'
		]);

		HeadingRowFormatter::default('none');
		$tabs = Excel::toCollection(new TechnicalDesignImport, request()->file('file_name'));

		if (\request()->clearAll) {
			$this->repo->truncate();
		}

		if (!\request()->importTabs) {
			$tabs = [$tabs[0]];
		}
		$count     = 0;
		$addedRows = [];
		foreach ($tabs as $tab) {
			$rows        = $this->repo->import($tab);
			$addedRows[] = $rows;
			$count       += count((array)$rows);
		}

		return [
			'count'     => $count,
			'addedRows' => $addedRows
		];
	}

	public function export()
	{
		return (new TechnicalDesignExport())->download('technical_design.xlsx');
	}

}
