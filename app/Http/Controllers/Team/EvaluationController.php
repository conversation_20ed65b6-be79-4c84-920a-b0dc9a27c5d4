<?php

namespace App\Http\Controllers\Team;

use App\Enums\Abilities;
use App\Enums\EvaluationAbilities;
use App\Enums\EvaluationStatus;
use App\Enums\Roles;
use App\Exports\EvalAnalyticsExport;
use App\Exports\EvalExport;
use App\Exports\EvalListExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\EvaluationRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Evaluation;
use App\Transformers\GeneralTransformer;
use Dingo\Api\Exception\ResourceException;
use Dingo\Api\Http\Request;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class EvaluationController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> <PERSON>deniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class EvaluationController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Evaluation::class;
	protected $loads       = [
		'show'  => ['team.photo.asset', 'technicalDesign', 'dynamicDrive'],
		'index' => ['team.photo.asset','dynamicDrive']
	];

	/**
	 * EvaluationController constructor.
	 *
	 * @param EvaluationRepositoryInterface $repo
	 */
	public function __construct(EvaluationRepositoryInterface $repo)
	{
		parent::__construct($repo);
		\DB::connection()->enableQueryLog();
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$id         = request()->route()->parameter('evaluation');
		$validators = [
			'team_id'   => ['integer', 'exists:teams,id'],
			'eval_date' => ['date'],
			'status'    => [Rule::in(EvaluationStatus::getValues())],
		];

		if (!$id) {
			$validators['team_id'][]   = 'required';
			$validators['eval_date'][] = 'required';
		}

		return $validators;
	}

	public function showPublic($teamId)
	{
		$team = app(TeamRepositoryInterface::class)->findByTeamNo($teamId);
		if (!$team) {
			throw new ResourceException('Team not found');
		}
		$id = $team->currentEvaluation->id;

		$model = $this->repoFind($id);
		if (isset($this->loads['show'])) {
			$model->load($this->loads['show']);
		}

		return $this->transform($model);
	}

	protected function repoFind($id)
	{
		/** @var Evaluation $eval */
		$eval = parent::repoFind($id);

		if (!$this->user() || $this->user()->isA(Roles::SUPERADMIN, Roles::DDK_ALL)) {
			$eval->details->load('criteria');

			return $eval;
		}

		$ab       = $this->user()->getAbilities();
		$filtered = $ab->filter(function ($item) {
			return $item->entity_type === Evaluation::class;
		})->pluck('name')->toArray();

		$details = $eval->details()
			->whereHas('criteria', function ($query) use ($filtered) {
				$query->whereIn('sub_category', $filtered);
			})
//			->whereIn('category', $filtered)
			->get();

		$details->load('criteria');

		$eval->setRelation('details', $details);

		return $eval;
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return mixed
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function updateDetail(Request $request, $id)
	{
		$evalDetail = $this->repo->findOrFailDetail($id);
		$this->repo->setUser($this->user);

		$this->authorize($evalDetail->criteria->sub_category, Evaluation::class);

		$data               = $this->validate([
			'value'         => 'nullable',
			'compatibility' => 'numeric|nullable',
			'notes'         => 'string|nullable|max:255',
			'penalty'       => 'numeric|nullable',
		]);
		$data['updated_by'] = $this->user()->id;

		if (isset($data['notes']) && !empty($data['notes'])) {
			$data['notes'] = $request->notes . ' | ' . $this->user()->first_name . " " . $this->user()->last_name;
//			$data['notes'] = strtok($request->notes, '|') . ' | ' . $this->user()->first_name . " " . $this->user()->last_name;
		}

		return $this->transform($this->repo->updateDetail($id, $data));
	}

	/**
	 * @param $id
	 * @return mixed
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function close(Request $request, $id)
	{
		$this->authorize(EvaluationAbilities::CLOSE_EVALUATION, $this->model);

		$this->repo->findMainModelOrFail($id);

		$this->repo->close();

		return $this->show($id);
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function reOpen(Request $request, $id)
	{
		$this->authorize(EvaluationAbilities::OPEN, $this->model);

		$this->repo->findMainModelOrFail($id);

		$this->repo->reopen();

		return $this->show($id);
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function checkSticker(Request $request, $id)
	{
		$response = $this->repo->findMainModelOrFail($id)->checkSticker(true);

		return $this->transform((object)$response);
	}

	/**
	 * @param Request $request
	 * @param         $id
	 * @return \Illuminate\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function giveSticker(Request $request, $id)
	{
		$this->authorize(EvaluationAbilities::STICKER, $this->model);

		$response = $this->repo->findMainModelOrFail($id)->giveSticker();

//		$this->startAfterRaceChecks($id);

		return $this->transform($response);
	}

	/**
	 * @param $id
	 * @return \Maatwebsite\Excel\BinaryFileResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function export($id)
	{
		$this->authorize(Abilities::LIST, $this->model);

		$this->repo->findMainModelOrFail($id);

		return $this->repo->export();
	}

	public function exportSuccess()
	{
		$this->authorize(Abilities::LIST, $this->model);

		$evaluations = $this->repo->getAll();
		foreach ($evaluations as $evaluation) {
			if ($evaluation->status !== 'success') {
				continue;
			}
			$filename = $evaluation->team->team_name . '_evaluation.xlsx';

			Excel::store(new EvalExport($evaluation), 'evals/' . $filename);
		}
	}

	/**
	 * @return void
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function startAllSuccessAfterRaceChecks()
	{
		$this->authorize(Abilities::CREATE, $this->model);

		$evaluations = $this->repo->getAll();
		foreach ($evaluations as $evaluation) {
			if ($evaluation->status !== 'success') {
				continue;
			}

			$this->startAfterRaceChecks($evaluation->id);
		}
	}

	public function exportEvalStatus()
	{
		$this->authorize(Abilities::LIST, $this->model);

		return (new EvalListExport())->download('evaluations_status.xlsx');
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function startAfterRaceChecks($id)
	{
		$this->authorize(Abilities::CREATE, $this->model);

		$this->repo->findMainModelOrFail($id)->createAfterRaceDetails();

		return $this->response->noContent();
	}

	/**
	 * @return mixed
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function evalAnalytics()
	{
		$this->authorize(Abilities::LIST, $this->model);

		return $this->repo->getEvalAnalytics();
	}

	/**
	 * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function exportAnalytics()
	{
		$this->authorize(Abilities::LIST, $this->model);

		return (new EvalAnalyticsExport())->download('analytics.xlsx');
	}

	/**
	 * @return mixed
	 * @throws \Illuminate\Auth\Access\AuthorizationException
	 */
	public function sendEvaluationStatusMails()
	{
		$this->authorize(Abilities::CREATE, $this->model);

		return $this->repo->sendAllEvaluationStatusMails();
	}

	/*public function export($id)
	{
		$evaluation = $this->repo->findOrFail($id);
		$evaluation->details;

		$file_name = 'Evaluation export';

//		return Excel::download(new EvalExport(), 'invoices.xls');


		$a = view('test', [
			'evaluations' => $evaluation->details
		]);
		$html          = 'test.html';

		file_put_contents($html, $a);

		$callStartTime = microtime(true);

		$objReader   = IOFactory::createReader('Html');
		$objPHPExcel = $objReader->load($html);

		$writer = IOFactory::createWriter($objPHPExcel, 'Xlsx');
		$writer->save('test.xlsx');

		return $a;
	}*/
}
