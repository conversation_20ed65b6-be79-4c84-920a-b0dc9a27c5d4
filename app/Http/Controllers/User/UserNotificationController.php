<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Interfaces\NotificationRepositoryInterface;
use App\Traits\TransformerTrait;
use App\Traits\UpdateUserTrait;
use App\Transformers\NotificationTransformer;

/**
 * Class UserNotificationController
 *
 * @package App\Http\Controllers\User
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UserNotificationController extends Controller
{
	use UpdateUserTrait;
	use TransformerTrait;

	protected $transformer = NotificationTransformer::class;

	/**w
	 *
	 * @var NotificationRepositoryInterface
	 */
	protected $notificationRepo;

	/**
	 * UserNotificationController constructor.
	 *
	 * @param NotificationRepositoryInterface $notificationRepo
	 */
	public function __construct(NotificationRepositoryInterface $notificationRepo)
	{
		$this->notificationRepo = $notificationRepo;
		$this->middleware(function ($request, $next) {
			$this->notificationRepo->setUser($this->user());

			return $next($request);
		});
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
//		$this->user()->notify(new CustomNotification([
//			'subject'    => 'subject',
//			'body'       => 'body',
//			'actionText' => 'actionText',
//			'actionURL'  => 'actionURL',
//			'footer'     => 'footer',
//		]));

		return $this->transform($this->notificationRepo->notifications());
	}

	/**
	 * Display unread notifications
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function unread()
	{
		return $this->transform($this->notificationRepo->unreadNotifications());
	}

	/**
	 * Display unread notifications
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function read()
	{
		return $this->transform($this->notificationRepo->readNotifications());
	}

	/**
	 * @param $id
	 * @return \Illuminate\Notifications\DatabaseNotification|mixed
	 */
	public function markAsRead($id)
	{
		return $this->transform($this->notificationRepo->markAsRead($id));
	}

}
