<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Interfaces\UserRepositoryInterface;
use App\Traits\UpdateUserTrait;
use App\Transformers\UserTransformer;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;

/**
 * Class UserController
 *
 * @package App\Http\Controllers\User
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UserController extends Controller
{
	use UpdateUserTrait;

	/**w
	 *
	 * @var UserRepositoryInterface
	 */
	protected $userRepo;

	/**
	 * UserController constructor.
	 *
	 * @param UserRepositoryInterface $eventRepo
	 */
	public function __construct(UserRepositoryInterface $userRepo)
	{
		$this->userRepo = $userRepo;
		$this->middleware(function ($request, $next) {
			$this->userRepo->setMainModel($this->auth->getUser());

			return $next($request);
		});
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @return Response
	 */
	public function show()
	{
		return $this->response->item($this->user(), new UserTransformer());
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param Request $request
	 * @return Response
	 * @throws ValidationException
	 */
	public function update(Request $request)
	{
		$this->updateUserProcess($request, $this->user()->id);

		return $this->show();
	}

	/**
	 * @return Response
	 * @throws Exception
	 */
	public function destroy()
	{
		$this->userRepo->removeUser($this->user());

		return $this->response->accepted();
	}

}
