<?php
namespace App\Http\Controllers\Stats;

use App\Http\Controllers\Controller;
use App\Interfaces\AppointmentRepositoryInterface;
use App\Interfaces\StatsRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Repositories\AppointmentRepository;
use App\Traits\TransformerTrait;

/**
 * Class StatsController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class StatsController extends Controller
{
	use TransformerTrait;
	/**
	 * @var StatsRepositoryInterface
	 */
	protected $repo;

	/**
	 * StatsController constructor.
	 *
	 * @param StatsRepositoryInterface $repo
	 */
	public function __construct(StatsRepositoryInterface $repo)
	{
		$this->repo = $repo;
	}

	public function stats()
	{
		return $this->repo->stats();
	}

	public function multiAppointmentTeam()
	{
		return $this->transform(app(TeamRepositoryInterface::class)->multiAppointmentTeam());
	}

	public function appointmentNotJoined()
	{
		return $this->transform(app(AppointmentRepositoryInterface::class)->appointmentNotJoined());
	}
}
