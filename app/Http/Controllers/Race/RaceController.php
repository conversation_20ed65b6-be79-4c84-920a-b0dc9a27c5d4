<?php
namespace App\Http\Controllers\Race;

use App\Exports\RaceAllScoreExport;
use App\Exports\RaceScoreExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\RaceRepositoryInterface;
use App\Models\Race;
use App\Transformers\GeneralTransformer;

/**
 * Class RaceController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class RaceController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Race::class;
	protected $loads       = [
		'show' => ['sessions', 'scores', 'allScores']
	];

	/**
	 * RaceController constructor.
	 *
	 * @param RaceRepositoryInterface $repo
	 */
	public function __construct(RaceRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$id         = request()->route()->parameter('race');
		$validators = [
			'name'        => ['string', 'unique:races'],
			'description' => ['string', 'nullable'],
			'start_date'  => ['date'],
			'ended'       => ['boolean'],
		];

		if (!$id) {
			$validators['name'][]       = 'required';
			$validators['start_date'][] = 'required';
		}

		return $validators;
	}

	public function endRace($id)
	{
		return $this->repo->findMainModelOrFail($id)->endRace();
	}

	public function calculateBestScore($id)
	{
		return $this->repo->findMainModelOrFail($id)->calculateBestScore();
	}

	public function exportScores($id)
	{
		$race = $this->repo->findOrFail($id);

		return (new RaceScoreExport($id))->download('EC_score_' . $race->name . '.xlsx');
	}

	public function exportAllScores($id)
	{
		$race = $this->repo->findOrFail($id);

		return (new RaceAllScoreExport($id))->download('EC_all_score_' . $race->name . '.xlsx');
	}

}
