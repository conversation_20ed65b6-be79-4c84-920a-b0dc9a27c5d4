<?php
namespace App\Http\Controllers\Race;

use App\Enums\SessionTeamStatus;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\SessionTeamRepositoryInterface;
use App\Models\SessionTeam;
use App\Transformers\GeneralTransformer;
use Illuminate\Validation\Rule;

/**
 * Class SessionTeamController
 *
 * @package App\Http\Controllers\Race
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class SessionTeamController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = SessionTeam::class;
	protected $loads       = [
		'show'  => ['team.penalties'],
		'index' => ['team'],
	];

	/**
	 * SessionScoreController constructor.
	 *
	 * @param SessionTeamRepositoryInterface $repo
	 */
	public function __construct(SessionTeamRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @param $data
	 * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model
	 */
	protected function repoCreate($data)
	{
		$result = parent::repoCreate($data);

		return $this->repo->afterUpdate($result);
	}

	/**
	 * @param      $data
	 * @param null $model
	 */
	protected function repoUpdate($data, $model = null)
	{
		parent::repoUpdate($data, $model);
		$result = $this->repoFind($model->id);

		return $this->repo->afterUpdate($result);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$id         = request()->route()->parameter('session_team');
		$validators = [
//			'team_id'               => ['integer', 'exists:teams,id'],
//			'session_id'            => ['integer', 'exists:sessions,id'],
			'start_point'         => ['nullable'],
			'race_time'           => ['nullable', 'regex:/^\d{1,3}\:[0-5]?[0-9](:[0-5]?[0-9])?$/'],
			'laps'                => ['integer'],
			'valid_laps'          => ['integer'],
			'initial_energy_cons' => ['numeric'],
			'last_energy_cons'    => ['numeric'],

			'initial_hydrogen_cons' => ['numeric'],
			'last_hydrogen_cons'    => ['numeric'],
//			'penalty'               => ['integer'],
			'final'                 => ['integer'],
			'score'                 => ['numeric'],
			'status'                => [Rule::in(SessionTeamStatus::getValues())],
		];

		$sessionId = request()->get('session_id', null);

		if (!$id) {
			$validators['team_id']    = ['required', 'integer', 'exists:teams,id'
				, 'unique:session_teams,team_id,NULL,team_id,session_id,' . $sessionId];
			$validators['session_id'] = ['required', 'integer', 'exists:sessions,id'];
		}

		return $validators;
	}

}
