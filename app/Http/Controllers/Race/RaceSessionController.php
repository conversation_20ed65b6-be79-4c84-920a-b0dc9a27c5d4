<?php
namespace App\Http\Controllers\Race;

use App\Enums\RaceType;
use App\Exports\SessionTeamExport;
use App\Http\Controllers\Admin\ResourceController;
use App\Interfaces\RaceSessionRepositoryInterface;
use App\Models\Race;
use App\Models\Session;
use App\Transformers\GeneralTransformer;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class RaceSessionController
 *
 * @package App\Http\Controllers\Team
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class RaceSessionController extends ResourceController
{
	protected $transformer = GeneralTransformer::class;
	protected $model       = Race::class;// important for roles
	protected $loads       = [
		'show' => ['raceTeams.team.penalties','race'],
		'index' => ['race']
	];

	/**
	 * RaceSessionController constructor.
	 *
	 * @param RaceSessionRepositoryInterface $repo
	 */
	public function __construct(RaceSessionRepositoryInterface $repo)
	{
		parent::__construct($repo);
	}

	/**
	 * @return array
	 */
	protected function validators()
	{
		$id         = request()->route()->parameter('race_session');
		$validators = [
			'race_id'     => ['integer', 'exists:races,id'],
			'name'        => ['nullable', 'string', 'unique:sessions'],
			'type'        => ['nullable', Rule::in(RaceType::getValues())],
			'description' => ['string','nullable'],
			'start_time'  => ['date'],
		];

		if (!$id) {
			$validators['race_id'][]    = 'required';
			$validators['name'][]       = 'required';
			$validators['start_time'][] = 'required';
//			$validators['type'][]       = 'required';
		}

		return $validators;
	}

	public function addRaceTeam($id)
	{
		$data = $this->validate([
			'teams'   => ['required', 'array'],
			'teams.*' => ['required', 'integer', 'exists:teams,id'],
		]);

		$this->repo
			->findMainModelOrFail($id)
			->addRaceTeam($data['teams']);
	}

	/**
	 * @param $id
	 * @return \Dingo\Api\Http\Response
	 */
	public function addAllTeams($id)
	{
		$result = $this->repo->findMainModelOrFail($id)->addPossibleTeamsToSession();

		return $this->transform($result);
	}

	public function exportSessionTeams($id)
	{
		/** @var Session $session */
		$session = $this->repoFind($id);

		$filename = $session->name . '_session_results.xlsx';

		return (new SessionTeamExport($session))->download($filename);
	}

}
