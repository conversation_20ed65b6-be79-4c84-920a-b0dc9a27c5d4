<?php

namespace App\Helpers;

class ReadInput
{

	public static function readMultiLine()
	{
		$data = '';
		while (true) {
			$line = fgets(STDIN);
			$data .= $line;
			if ($line == "\n") {
				break;
			}
		}

		return $data;
	}

	public static function confirmInput()
	{
		echo 'continue? Y - N :';
		$line = fgets(STDIN);
		if (trim($line) == 'Y' || trim($line) == 'y') {
			return true;
		}

		return false;
	}
}
