<?php
namespace App\Helpers;

use App\Enums\EvaluationDetailFunctions;

class EvalHelper
{
	/**
	 * @param $value
	 * @return object
	 */
	public static function parseFunction($value)
	{
		preg_match('/(\w+)\s*\((\w+);(\s*[\d\.]+)\s*(\w*)(;!#([\d\.]+))?\)/ui', $value, $matches);
		// Ceza(Sabit;0.5Wh)

		return (object)[
			'function' => isset($matches[1]) ? (EvaluationDetailFunctions::get($matches[1]) ?: $matches[1]) : null,
			'type'     => isset($matches[2]) ? (EvaluationDetailFunctions::getTypes($matches[2]) ?: $matches[2]) : null,
			'value'    => isset($matches[3]) ? $matches[3] : null,
			'unit'     => isset($matches[4]) ? $matches[4] : null,
			'ARParent'     => isset($matches[6]) ? $matches[6] : null,
		];
	}

}
