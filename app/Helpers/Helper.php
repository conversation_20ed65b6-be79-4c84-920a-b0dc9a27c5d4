<?php
namespace App\Helpers;

use App\Interfaces\SettingsRepositoryInterface;

/**
 * Class Helper
 *
 * @package App\Helpers
 * <AUTHOR> Ak<PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class Helper
{
	/**
	 * @param string $pattern
	 * @param array  $values
	 * @return string
	 */
	public static function build($pattern, $values)
	{
		foreach ($values as $key => $value) {
			$pattern = str_replace('%' . $key . '%', $value, $pattern);
		}

		return $pattern;
	}

	/**
	 * @param      $key
	 * @param null $default
	 * @return mixed
	 */
	public static function settings($key, $default = null)
	{
		return app(SettingsRepositoryInterface::class)->get($key, $default);
	}

	/**
	 * @param      $array
	 * @param      $key
	 * @param null $default
	 * @return mixed|null
	 */
	public static function get($array, $key, $default = null)
	{
		return isset($array[$key]) ? $array[$key] : null;
	}

}
