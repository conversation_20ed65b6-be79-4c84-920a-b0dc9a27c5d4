<?php

namespace App\Helpers;

use Artisaninweb\SoapWrapper\Client;

class SoapClient extends Client
{
	function __doRequest($request, $location, $action, $version, $oneWay = 0)
	{
		$namespace = "tr";

		$request = str_replace('ns1:', $namespace . ':', $request);
		$request = str_replace(':ns1', ':' . $namespace, $request);
		$request = str_replace('SOAP-ENV', 'soap', $request);

		$location = str_replace('http', 'https', $location);
		$location = str_replace(':80', '', $location);

		// parent call
		return parent::__doRequest($request, $location, $action, $version, $oneWay);
	}

}
