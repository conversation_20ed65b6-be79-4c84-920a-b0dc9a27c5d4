<?php

namespace App\Helpers;

use App\Interfaces\HesCodeServiceInterface;
use App\Models\HesCode;
use Symfony\Component\HttpKernel\Exception\HttpException;

class HesCodeService implements HesCodeServiceInterface
{
	protected $soapWrapper;

	public function __construct(SoapWrapper $soapWrapper)
	{
		$this->soapWrapper = $soapWrapper;
		$this->soapWrapper->configure();
	}

	/**
	 * @param $hesCode
	 * @return array|object
	 */
	public function checkHesCode($hesCode)
	{
		$hesCode = $this->cleanCode($hesCode);
		try {
			$data = $this->soapWrapper->call('hes.checkHESCode', [[
				'hes_code' => $hesCode
			]]);

			return $this->checkResponse($data);
		} catch (\SoapFault $e) {
			throw new HttpException(503, \Lang::get('An error occurred while hes code checking, :error', [
				'error' => $e->getMessage()
			]));
		}
	}

	/**
	 * @param array $hesCodes
	 * @return array|bool|object
	 */
	public function checkHesCodes(array $hesCodes)
	{
		try {
			$data = $this->soapWrapper->call('hes.checkHESCodes', [[
				'hes_code_listesi' => $this->cleanCodes($hesCodes)
			]]);

			return $this->checkResponse($data);
		} catch (\SoapFault $e) {
			throw new HttpException(503, \Lang::get('An error occurred while hes code checking, :error', [
				'error' => $e->getMessage()
			]));
		}
	}

	/**
	 * status RISKLESS|RISKY|hescodenotfound|hescodehasbeenexpired
	 *
	 * @param $response
	 * @return array|object|null
	 */
	protected function checkResponse($response)
	{
		if (isset($response->hesKodBilgileri)) {
			if (is_array($response->hesKodBilgileri)) {
				return array_map([$this, 'hesCodeStatus'], $response->hesKodBilgileri);
			}

			return $this->hesCodeStatus($response->hesKodBilgileri);
		}

		return null;
	}

	/**
	 * @param $hes
	 * @return object
	 */
	protected function hesCodeStatus($hes)
	{
		if (isset($hes->errorKey)) {
			return (object)[
				'status'        => $hes->errorKey,
				'hesCodeStatus' => $hes->errorKey
			];
		}

		return (object)array_merge([
			'status' => isset($hes->hesCodeStatus) ? $hes->hesCodeStatus : $hes->current_health_status
		], (array)$hes);
	}

	private function cleanCodes($hesCodes)
	{
		$newCodes = [];
		foreach ($hesCodes as $hesCode) {
			$newCodes[] = $this->cleanCode($hesCode);
		}

		return $newCodes;
	}

	private function cleanCode($hesCodes)
	{
		return strtoupper(str_replace('-', '', $hesCodes));
	}

	/**
	 * @param $hesResult
	 * @return mixed
	 */
	public function insertHesResult($hesResult)
	{
		$collection = new \Illuminate\Database\Eloquent\Collection();
		foreach ($hesResult as $item) {
			$model = new HesCode([
				'hes_code' => $item->hesCode,
				'status'   => $item->status,
				'response' => json_encode($item)
			]);
			$collection->push($model);
		}

		HesCode::insert($collection->toArray());

		return $collection;
	}

}
