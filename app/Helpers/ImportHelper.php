<?php
namespace App\Helpers;

use Illuminate\Support\Facades\Validator;

class ImportHelper
{
	/**
	 * @param $value
	 * @return object
	 */
	public static function createData($collection, $rules, $map, $cast = [])
	{

		$rows = [];
		foreach ($collection->first() as $i => $row) {
			$data = [];
			foreach ($map as $index => $item) {
				$func         = isset($cast[$index]) ? $cast[$index] : 'trim';
				$data[$index] = $row->has($item) ? $func($row[$item]) : null;
			}
			$values = array_values($data);
			if (!count(array_filter($values, fn($value) => !empty($value)))) {
				continue;
			}

			$validator = Validator::validate($data, $rules);
//			if ($validator->fails()) {
//				$errors[$i] = ['errors' => $validator->errors(), 'data' => $data];
//				continue;
//			}
			$rows[] = $data;
		}

		return $rows;
	}

}
