<?php

namespace App\Helpers;

use Artisaninweb\SoapWrapper\Exceptions\ServiceNotFound;
use Artisaninweb\SoapWrapper\Service;
use Closure;
use SimpleXMLElement;
use SoapVar;

class SoapWrapper extends \Artisaninweb\SoapWrapper\SoapWrapper
{
	/**
	 * @param $key
	 * @return Service
	 */
	public function getService($key)
	{
		return isset($this->services[$key]) ? $this->services[$key] : null;
	}

	/**
	 * Get the client
	 *
	 * @param string  $name
	 * @param Closure $closure
	 *
	 * @return mixed
	 * @throws ServiceNotFound
	 */
	public function client($name, Closure $closure = null)
	{
		if ($this->has($name)) {
			/** @var Service $service */
			$service = $this->services[$name];

			if (is_null($service->getClient())) {
				$client = new SoapClient($service->getWsdl(), $service->getOptions(), $service->getHeaders());

				$service->client($client);
			} else {
				$client = $service->getClient();
			}

			return $closure($client);
		}

		throw new ServiceNotFound("Service '" . $name . "' not found.");
	}

	public function configure()
	{

		$credentials = [
			'userName'   => env('HES_SERVICE_USERNAME'),
			'password'   => env('HES_SERVICE_PASSWORD'),
			'origin'     => env('HES_SERVICE_ORIGIN'),
			'randomizer' => env('HES_SERVICE_RANDOMIZER'),
			'token'      => env('HES_SERVICE_TOKEN'),
		];

		$xml = new SimpleXMLElement('<root/>');
		array_walk_recursive($credentials, function ($key, $value) use ($xml) {
			$xml->addChild($value, $key);
		});

		preg_match('/<root>(.*)<\/root>/', $xml->asXML(), $match);
		$test = new SoapVar('<tr:userAuth>' . $match[1] . '</tr:userAuth>', XSD_ANYXML);

		$this->add('hes', function ($service) use ($test) {
			$service
				->wsdl(env('HES_SERVICE_WSDL', 'https://wsbroker.tubitak.gov.tr/wsbroker/TBTKHESKodServisFW?wsdl'))
				->header(env('HES_SERVICE_NS', 'http://tr.gov.tubitak.wsbroker.tbtkheskodservicefw'), 'tr:userAuth', $test)
				->trace(true);
		});
	}

}
