<?php

namespace App\Exports;

use App\Models\Session;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class SessionTeamExport implements FromCollection, ShouldAutoSize, WithHeadings, WithStrictNullComparison
{
	use ExportableTrait;
	/**
	 * @var Session
	 */
	protected $session;

	/**
	 * SessionTeamExport constructor.
	 *
	 * @param $session
	 */
	public function __construct($session)
	{
		$this->session = $session;
	}

	public function collection()
	{
		$scores = $this->session->raceTeams;
		$scores->load('team');
		$collection = new Collection();
		foreach ($scores as $prom) {
			$collection->push([
				'session_id'            => $prom->session_id,
				'university'            => $prom->team->university_name,
				'team'                  => $prom->team->team_name,
				'number'                => $prom->team->vehicle_number,
				'start_point'           => $prom->start_point,
				'laps'                  => $prom->laps,
				'valid_laps'            => $prom->valid_laps,
				'initial_energy_cons'   => $prom->initial_energy_cons,
				'last_energy_cons'      => $prom->last_energy_cons,
				'total_cons'            => $prom->total_cons,
				'initial_hydrogen_cons' => $prom->initial_hydrogen_cons,
				'last_hydrogen_cons'    => $prom->last_hydrogen_cons,
				'total_hydrogen_cons'   => $prom->total_hydrogen_cons,
				'status'                => $prom->status,
				'penalty'               => $prom->penalty,
				'final'                 => $prom->final,
				'score'                 => $prom->score,
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			"Session id",
			"University",
			"Team",
			"No",
			"Start Point",
			"Laps",
			"Valid Laps",
			"Energy Consumption Initial Value",
			"Energy Consumption Last Value",
			"Total Consumption",
			"Hydrogen Consumption Initial Value",
			"Hydrogen Consumption Last Value",
			"Total Hydrogen Consumption",
			"Status",
			"Penalty/Prize",
			"Final Consumption",
			"Score",
		];
	}

}

