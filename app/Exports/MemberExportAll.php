<?php

namespace App\Exports;

use App\Interfaces\MemberRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class MemberExportAll implements FromCollection, ShouldAutoSize, WithHeadings, WithStrictNullComparison
{
	use ExportableTrait;

	protected function getMembers()
	{
		return app(MemberRepositoryInterface::class)->getAllMembers();
//		$teams = app(TeamRepositoryInterface::class)->getApprovedTeams();

//		return app(MemberRepositoryInterface::class)
//			->getAllMembersFromTeamIds($teams->pluck('id')->toArray());
	}

	public function collection()
	{
		$members = $this->getMembers();

		$collection = new Collection();
		foreach ($members as $prom) {
			$collection->push([
				"first_name"      => $prom->first_name,
				"last_name"       => $prom->last_name,
				"team_id"         => $prom->team->team_id,
				"team"            => $prom->team->team_name,
				"vehicle_number"  => $prom->team->vehicle_number,
				"vehicle_name" => $prom->team->vehicle_name,
				"role_in_team"    => $prom->role_in_team,
				"identity_number" => $prom->identity_number,
				"email"           => $prom->email,
				"phone_number"    => $prom->phone_number,
				"birthday"        => $prom->birthday,
				"gender"          => $prom->gender,
				"in_area"         => $prom->in_area,
				"parent_name"     => $prom->parent_name,
				"parent_phone"    => $prom->parent_phone,
				"uniform_size"    => $prom->uniform_size,
//				"hes_code"        => $prom->hes_code,
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return array_map(function ($val) {
			return \Lang::get('validation.attributes.' . $val);
		}, [
			"first_name",
			"last_name",
			"team_id",
			"team",
			"vehicle_number",
			"vehicle_name",
			"role_in_team",
			"identity_number",
			"email",
			"phone_number",
			"birthday",
			"gender",
			"in_area",
			"parent_name",
			"parent_phone",
			"uniform_size",
//			"hes_code",
		]);
	}
}
