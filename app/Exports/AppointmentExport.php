<?php

namespace App\Exports;

use App\Enums\SchoolType;
use App\Models\Appointment;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AppointmentExport implements FromCollection, ShouldAutoSize, WithHeadings
{
	use ExportableTrait;

	public function collection()
	{
		$proms      = Appointment::with('team')->get();
		$collection = new Collection();
		foreach ($proms as $prom) {
			$collection->push([
				'id'            => $prom->id,
				'team_id'       => $prom->team->team_id,
				'team'          => $prom->team->team_name,
				'team_category' => $prom->team->vehicle_category,
				'school_type'   => SchoolType::toHumanReadable($prom->team->school_type),
				'start_time'    => $prom->start_time,
				'status'        => $prom->status,
				'comments'      => $prom->comments,
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'No',
			'Team Id',
			'Team Name',
			'Team Category',
			'School Type',
			'Start Time',
			'Status',
			'Comments',
		];
	}
}
