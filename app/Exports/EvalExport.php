<?php

namespace App\Exports;

use App\Enums\EvaluationCompatibility;
use App\Models\Evaluation;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Events\AfterSheet;

class EvalExport implements FromCollection, WithHeadings, WithStrictNullComparison, WithEvents
{
	use ExportableTrait;

	protected $eval;

	/**
	 * EvalExport constructor.
	 *
	 * @param $eval
	 */
	public function __construct(Evaluation $eval)
	{
		$this->eval = $eval;
	}

	public function collection()
	{

		$details = $this->eval->details;
		$this->eval->load(['details.criteria']);
		$collection = new Collection();
		foreach ($details as $detail) {
			$collection->push([
				'vehicle_number' => $this->eval->team->vehicle_number,
				'team_name'      => $this->eval->team->team_name,
				'category'       => $detail->criteria->category,
				'subject'        => $detail->criteria->subject,
				'condition'      => $detail->criteria->content,
//				'value'          => $detail->value,
				'compatibility'  => EvaluationCompatibility::getKeyName($detail->compatibility),
				'penalty'        => $detail->penalty,
//				'notes'          => $detail->notes,
//				'updated_by'     => $detail->updatedBy ? $detail->updatedBy->full_name : null,
//				'updated_at'     => $detail->updated_at
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'Vehicle Number',
			'Team name',
			'Category',
			'Subject',
			'Condition',
//			'Value',
			'Compatibility',
			'Penalty(negative means Award)',
//			'Notes',
//			'DDK',
//			'updated at'
		];
	}

	/**
	 * @return array
	 */
	public function registerEvents(): array
	{
		return [
			AfterSheet::class => function (AfterSheet $event) {
				$event->sheet->getDelegate()->getColumnDimension('A')->setWidth(10);
				$event->sheet->getDelegate()->getColumnDimension('B')->setWidth(16);
				$event->sheet->getDelegate()->getColumnDimension('C')->setWidth(16);
				$event->sheet->getDelegate()->getColumnDimension('D')->setWidth(20);
				$event->sheet->getDelegate()->getColumnDimension('E')->setWidth(60);
			},
		];
	}
}
