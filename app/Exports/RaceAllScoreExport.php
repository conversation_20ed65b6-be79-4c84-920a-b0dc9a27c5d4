<?php

namespace App\Exports;

use App\Interfaces\RaceRepositoryInterface;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RaceAllScoreExport implements FromCollection, ShouldAutoSize, WithHeadings
{
	use ExportableTrait;

	public function __construct($raceId)
	{
		$this->raceId = $raceId;
	}

	public function collection()
	{

		$race       = app(RaceRepositoryInterface::class)->findOrFail($this->raceId);
		$scores     = $race->allScores;
		$collection = new Collection();
		foreach ($scores as $score) {
			$collection->push([
				'vehicle_number'      => $score->team->vehicle_number,
				'team_name'           => $score->team->team_name,
				'session'             => $score->session->name,
				'session_time'        => $score->session->start_time,
				'race_time'           => $score->race_time,
				'laps'                => $score->laps,
				'valid_laps'          => $score->valid_laps,
				'total_cons'          => $score->total_cons,
				'total_hydrogen_cons' => $score->total_hydrogen_cons,
				'penalty'             => $score->penalty,
				'score'               => $score->score,
				'status'              => $score->status,
			]);
		}

//		return $scores;
		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'Vehicle Number',
			'Team Name',
			'Session',
			'Session Created',
			'Race Time',
			'Laps',
			'Valid Laps',
			'Consumption',
			'Hydrogen Cons',
			'penalty',
			'Score',
			'Status',
		];
	}
}
