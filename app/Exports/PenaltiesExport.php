<?php

namespace App\Exports;

use App\Enums\SchoolType;
use App\Interfaces\PenaltyRepositoryInterface;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class PenaltiesExport implements FromCollection, ShouldAutoSize, WithHeadings, WithStrictNullComparison
{
	use ExportableTrait;

	public function collection()
	{
		$penalties = app(PenaltyRepositoryInterface::class)->getAll();
//		$penalties = $penalties->filter(function ($item){
//			return $item->session_id != 1;
//		});  2. session icin kullanilmmisti, 1'i filterledik

		$group      = $penalties->groupBy('team_id');
		$collection = new Collection();
		/** @var Collection $prom */
		foreach ($group as $prom) {

			$team      = $prom->first()->team;
			$eval      = $team->currentEvaluation;
			$violation = $prom->pluck('violation')
				->map(function ($r) {
					return str_replace("\n", '', $r);
				})
				->join("\r\n");

			$subject = $prom->pluck('subject')->join("\r\n");
			$penalty = $prom->pluck('penalty')->join("\r\n");
			$total   = $prom->pluck('penalty')->sum();
			$collection->push([
				'team_id'           => $team->team_id,
				'team_name'         => $team->team_name,
				'team_approved'     => $team->completed,
				'evaluation_status' => $eval ? $eval->status : 'not_joined',
				'university_name'   => $team->university_name,
				'vehicle_category'   => $team->vehicle_category,
				'school_type'       => SchoolType::toHumanReadable($team->school_type),
				'vehicle_number'    => $team->vehicle_number,
				'vehicle_name'      => $team->vehicle_name,
				'subject'           => $subject,
				'violation'         => $violation,
				'penalty'           => $penalty,
				'total_penalty'     => $total,
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'team_id',
			'Team Name',
			'Team Approved',
			'Evaluation Status',
			'University Name',
			'Vehicle Category',
			'School Type',
			'Vehicle Number',
			'Vehicle Name',
			'Subject',
			'Violation',
			'Penalty',
			'Total Penalty',
		];
	}
}
