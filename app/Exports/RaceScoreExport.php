<?php

namespace App\Exports;

use App\Interfaces\RaceRepositoryInterface;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RaceScoreExport implements FromCollection, ShouldAutoSize, WithHeadings
{
	use ExportableTrait;

	public function __construct($raceId)
	{
		$this->raceId = $raceId;
	}

	public function collection()
	{

		$race       = app(RaceRepositoryInterface::class)->findOrFail($this->raceId);
		$scores     = $race->scores;
		$collection = new Collection();
		foreach ($scores as $score) {
			$collection->push([
				'vehicle_number'      => $score->team->vehicle_number,
				'team_name'           => $score->team->team_name,
				'laps'                => $score->laps,
				'valid_laps'          => $score->valid_laps,
				'total_cons'          => $score->total_cons,
				'total_hydrogen_cons' => $score->total_hydrogen_cons,
				'penalty'             => $score->penalty,
				'score'               => $score->score,
			]);
		}

//		return $scores;
		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'Vehicle Number',
			'Team Name',
			'Laps',
			'Valid Laps',
			'Consumption',
			'Hydrogen Cons',
			'penalty',
			'Score',
		];
	}
}
