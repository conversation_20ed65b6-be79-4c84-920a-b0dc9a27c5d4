<?php

namespace App\Exports;

use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\Interfaces\EvaluationRepositoryInterface;

class EvalAnalyticsExport implements FromCollection, ShouldAutoSize, WithHeadings
{
	use ExportableTrait;

	protected function getAnalytics()
	{

		return app(EvaluationRepositoryInterface::class)->getEvalAnalytics();

	}

	protected function getElementCount($arr, $val)
	{
		$temp = explode(",", $arr);
		$count = 0;
		foreach ($temp as $current){
			if ($current == $val) {
				$count++;
			}
		}

		return $count;

	}

	public function collection()
	{

		$evalAnalytics = $this->getAnalytics();

		$collection = new Collection();

		foreach ($evalAnalytics["criterias"] as $criteriaAnalytics) {
			//	print_r($criteriaAnalytics);
			$collection->push([
				'criteria_id'       => $criteriaAnalytics->criteria_id,
				'criteria_category' => $criteriaAnalytics->criteria->category,
				'criteria_subject'  => $criteriaAnalytics->criteria->subject,
				'criteria_total'  => count(explode(",", $criteriaAnalytics->compatibilities)),
				'criteria_bos'  => $this->getElementCount($criteriaAnalytics->compatibilities,0),
				'criteria_uygun'  => $this->getElementCount($criteriaAnalytics->compatibilities,1),
				'criteria_kusurlu'  => $this->getElementCount($criteriaAnalytics->compatibilities,2),
				'criteria_disk'  => $this->getElementCount($criteriaAnalytics->compatibilities,3),


			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'Kriter ID',
			'Kriter Kategorisi',
			'Kriter',
			'Toplam',
			'Boş',
			'Uygun',
			'Kusurlu',
			'Diskalifiye',
		];
	}
}
