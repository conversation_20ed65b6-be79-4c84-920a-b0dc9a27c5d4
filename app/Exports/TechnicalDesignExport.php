<?php

namespace App\Exports;

use App\Models\TechnicalDesign;
use App\Traits\ExportableTrait;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class TechnicalDesignExport implements FromCollection, WithHeadings, WithEvents
{
	use ExportableTrait;

	protected $columns = [
		'id',
		'takim_no',
		'toplam',
		'arac_ozellikleri_tablosu',
		'motor',
		'motor_surucusu',
		'batarya_yonetim_sistemi',
		'yerlesik_sarj_birimi',
		'batarya_paketleme',
		'elektronik_diferansiyel_uygulamasi',
		'arac_kontrol_sistemi',
		'izolasyon_izleme_cihazi',
		'direksiyon_sistemi',
		'kapi_mekanizmasi',
		'mekanik_detaylar',
		'arac_elektrik_semasi',
		'orijinal_tasarim',
		'yerlilik_durumu',
	];

	public function collection()
	{
		return TechnicalDesign::all($this->columns);
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		$columns = [];
		foreach ($this->columns as $item) {
			$words = [];
			foreach (explode('_', $item) as $part) {
				$words[] = ucwords($part);
			}

			$columns[] = implode(' ', $words);
		}

		return $columns;
	}

	/**
	 * C D -> universite takim_adi
	 * X aciklama | too long
	 *
	 * @return array
	 */
	public function registerEvents(): array
	{
		return [
			AfterSheet::class => function (AfterSheet $event) {
				$event->sheet->getDelegate()->getColumnDimension('C')->setAutoSize(true);
				$event->sheet->getDelegate()->getColumnDimension('D')->setAutoSize(true);
				$event->sheet->getDelegate()->getColumnDimension('X')->setAutoSize(false);
				foreach ($event->sheet->getDelegate()->getRowIterator() as $row) {
					$event->sheet->getDelegate()->getRowDimension($row->getRowIndex())->setRowHeight(20);
				}
			},
		];
	}
}
