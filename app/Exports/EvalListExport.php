<?php

namespace App\Exports;

use App\Enums\SchoolType;
use App\Models\Evaluation;
use App\Traits\ExportableTrait;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class EvalListExport implements FromCollection, ShouldAutoSize, WithHeadings
{
	use ExportableTrait;

	public function collection()
	{

		$evals = Evaluation::with('team')->orderBy('status')->get();

		$collection = new Collection();
		foreach ($evals as $eval) {
			$collection->push([
				'vehicle_number'    => $eval->team->vehicle_number,
				'vehicle_name'      => $eval->team->vehicle_name,
				'vehicle_category'  => $eval->team->vehicle_category,
				'team_id'           => $eval->team->team_id,
				'team_name'         => $eval->team->team_name,
//				'team_leader_email' => $eval->team->team_leader_email,
				'school_type'       => SchoolType::toHumanReadable($eval->team->school_type),
				'university_name'   => $eval->team->university_name,
				'status'            => $eval->status,
				'eval_day'          => $eval->eval_day,
				'Dynamic Drive'     => $eval->dynamicDrive ? 'passed' : 'failed',
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'Vehicle Number',
			'Vehicle Name',
			'Vehicle Category',
			'Team Id',
			'Team Name',
//			'Team Leader Email',
			'School Type',
			'School Name',
			'Status',
			'Evaluation Day',
			'Dynamic Drive',
		];
	}
}
