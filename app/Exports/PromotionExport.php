<?php

namespace App\Exports;

use App\Models\Promotion;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PromotionExport implements FromCollection, ShouldAutoSize, WithHeadings
{

	public function collection()
	{
		$proms = Promotion::orderBy('team_id')->get();
		$proms->load('team');
		$collection = new Collection();
		foreach ($proms as $prom) {
			$collection->push([
				'Team'     => $prom->team->team_name,
				'Product'  => $prom->name,
				'Quantity' => $prom->quantity,
				'Date'     => new Carbon($prom->created_at),
			]);
		}

		return $collection;
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'Team',
			'Product',
			'Quantity',
			'Date',
		];
	}
}

