<?php

namespace App\Exports;

use App\Enums\AppType;
use App\Models\Team;
use App\Traits\ExportableTrait;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TeamExport implements FromCollection, ShouldAutoSize, WithHeadings
{
	use ExportableTrait;

	public function collection()
	{
		return Team::all([
			'id',
			'team_id',
			'team_name',
			'university_name',
			'vehicle_name',
			'city_name',
			'vehicle_number',
			'vehicle_category',
			'completed',
			'team_member_count',
			'team_leader',
			'team_leader_phone',
			'team_leader_email',
			'school_type',

//			'consultant_name',
//			'consultant_phone',
//			'consultant_email',

//			'curator_name',
//			'curator_phone',
//			'curator_email',

//			'driver_name',
//			'driver_phone',
//			'driver_email',

//			'second_driver_name',
//			'second_driver_phone',
//			'second_driver_email',
		])->append(['team_status']);
	}

	/**
	 * @inheritDoc
	 */
	public function headings(): array
	{
		return [
			'No', //	'No',
			'Team Id', //	'Takım ID',
			'Team Name', //	'Takım Adı',
			'University Name', //	'Üniversitesi Adı',
			'Vehicle Name', //	'Araç Adı',
			'City Name', //	'Şehir Adı',
			'Vehicle Number', //	'Araç Numarası',
			'Category', //	'Araç Kategorisi (Elektromobil/Hidromobil)',
			'Approved', //	'Araç Kategorisi (Elektromobil/Hidromobil)',
			'Number of Team Members', //	'Takım Kişi Sayısı',
			!AppType::isLise() ? 'Team Captain Name' : 'Consultant Teacher Name', //	'Takım Kaptanı Adı Soyadı',
			!AppType::isLise() ? 'Team Captain Phone' : 'Consultant Teacher Phone', //	'Takım Kaptanı Telefon No',
			!AppType::isLise() ? 'Team Captain Email' : 'Consultant Teacher Email', //	'Takım Kaptanı E-posta',
			'School Type',
//			'Academic Advisor Name', //	'Akademik Danışman Adı Soyadı',
//			'Academic Advisor  Phone', //	'Akademik Danışman Telefon No',
//			'Academic Advisor Phone', //	'Akademik Danışman E-posta',
//			'Substitute Academic Advisor Name', //	'Akademik Sorumlu Adı Soyadı',
//			'Substitute Academic Advisor Phone', //	'Akademik Sorumlu Telefon No',
//			'Substitute Academic Advisor Email', //	'Akademik Sorumlu E-posta',
//			'Driver Name', //	'Sürücü Adı Soyadı',
//			'Driver Phone', //	'Sürücü Telefon No',
//			'Driver Email', //	'Sürücü E-posta',
//			'Reserve Driver Name', //	'Yedek Sürücü Adı Soyadı',
//			'Reserve Driver Phone', //	'Yedek Sürücü Telefon No',
//			'Reserve Driver Email', //	'Yedek Sürücü E-posta',
			'logo',
			'Team Status'
		];
	}
}
