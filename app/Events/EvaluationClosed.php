<?php

namespace App\Events;

use App\Models\Evaluation;
use App\Models\User;
use Illuminate\Queue\SerializesModels;

class EvaluationClosed
{
	use SerializesModels;

	/**
	 * @var User|\Illuminate\Contracts\Auth\Authenticatable
	 */
	public $evaluation;

	/**
	 * Create a new event instance.
	 *
	 * @param \Illuminate\Contracts\Auth\Authenticatable $evaluation
	 * @return void
	 */
	public function __construct(Evaluation $evaluation)
	{
		$this->evaluation = $evaluation;
	}
}
