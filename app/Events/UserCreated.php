<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Queue\SerializesModels;

class UserCreated
{
	use SerializesModels;

	/**
	 * @var User|\Illuminate\Contracts\Auth\Authenticatable
	 */
	public $user;

	/**
	 * Create a new event instance.
	 *
	 * @param \Illuminate\Contracts\Auth\Authenticatable $user
	 * @return void
	 */
	public function __construct(User  $user, $pass)
	{
		$this->user = $user;
		$this->pass = $pass;
	}
}
