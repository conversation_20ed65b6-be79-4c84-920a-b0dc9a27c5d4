<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

/**
 * Class ClearRoles
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class ClearRoles extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'roles:clear';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'clear user,role,abilities';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info('clearing...');

		\DB::statement('SET FOREIGN_KEY_CHECKS = 0');
		\DB::statement('TRUNCATE TABLE assigned_roles');
		\DB::statement('TRUNCATE TABLE roles');
		\DB::statement('TRUNCATE TABLE permissions');
		\DB::statement('TRUNCATE TABLE abilities');

		\DB::statement('SET FOREIGN_KEY_CHECKS = 1');

		$this->info('cleared');

		return 0;
	}
}
