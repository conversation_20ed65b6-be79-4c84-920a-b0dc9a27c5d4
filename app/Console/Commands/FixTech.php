<?php

namespace App\Console\Commands;

use App\Interfaces\TeamRepositoryInterface;
use App\Interfaces\TechnicalDesignRepositoryInterface;
use Illuminate\Console\Command;

/**
 * Class FixTech
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class FixTech extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'tech:fix';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'bug fix';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info('starting...');
		$teams    = app(TeamRepositoryInterface::class)->getAll();
		$techRepo = app(TechnicalDesignRepositoryInterface::class);

		foreach ($teams as $team) {
			$techRepo->calculateTTRTotal($team);
		}

		$this->info('OK');
	}
}
