<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

/**
 * Class ClearUsers
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class ClearUsers extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'users:clear';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'clear all users';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info('clearing...');

		\DB::statement('TRUNCATE TABLE users');
		\DB::statement('TRUNCATE TABLE user_details');

		$this->info('cleared');

		return 0;
	}
}
