<?php

namespace App\Console\Commands;

use App\Interfaces\CriteriaRepositoryInterface;
use App\Models\Evaluation;
use Bouncer;
use Illuminate\Console\Command;

/**
 *
 */
class BuildRoles extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'criteria:roles';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Create criteria roles';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info('starting...');

		$criteriaRepo = app(CriteriaRepositoryInterface::class);
		$criterias = $criteriaRepo->getAllCategories();

		foreach ($criterias as $criteria) {
			Bouncer::allow('DDK '.$criteria->category)->to([$criteria->sub_category], Evaluation::class);
		}

		$this->info('OK');
	}
}
