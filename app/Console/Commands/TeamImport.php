<?php

namespace App\Console\Commands;

use App\Enums\AppType;
use App\Enums\MemberType;
use App\Enums\VehicleCategory;
use App\Interfaces\MemberRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Member;
use App\Models\Team;
use App\Repositories\MemberRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

/**
 * Class TeamIDMake
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class TeamImport extends Command
{

	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */

	protected $signature = 'import:team {--offset=1800} {--page=1} {--limit=100}';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'team import from KYS data';

	/**
	 * @var TeamRepositoryInterface
	 */
	protected $repo;
	/**
	 * @var MemberRepositoryInterface
	 */
	protected $memberRepo;

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function __construct(TeamRepositoryInterface $repo)
	{
		$this->repo       = $repo;
		$this->memberRepo = app(MemberRepositoryInterface::class);

		parent::__construct();
	}

	/**
	 * Execute the console command.
	 *
	 * @return int
	 */
	public function handle()
	{
		$this->info('starting...');

		$this->info('authentication');

		$limit  = $this->option('limit'); //100;
		$offset = $this->option('offset'); // 1600;
		$page   = $this->option('page'); // 3

		$activeTeams = [
			464451,
			468679,
			458726,
			468070,
			467331,
			467929,
			411616,
			423262,
			467671,
			421209,
			461913,
			468007,
			412447,
			468892,
			468879,
			420824,
			412597,
			447648,
			459623,
			396631,
			468141,
			415893,
			467529,
			468709,
			464344,
			466828,
			466140,
			419941,
			465679,
			420717,
			459660,
			378712,
			464756,
			466333,
			468659,
			452089,
			468492,
			463134,
			398374,
			415935,
			465682,
			418153,
			465513,
			419504,
			465250,
			464204,
			466228,
			465668,
			466185,
			465622,
			466257,
			467481,
			468719,
			392855,
			456458,
			466536,
			445542,
			464947,
			465978,
			466593,
			468525,
			423117,
			465763,
			467588,
			404994,
			451299,
			458869,
			465801,
			466605,
			425047,
			468823,
			412513,
			467948,
			464753,
			424312,
			468526,
			419542,
			463710,
			403493,
			461164,
			466146,
			468382,
			436155,
			465186,
			424143,
			467205,
			314454,
			466406,
			417266,
			426387,
			463661,
			436470,
			465366,
			468925,
			468259,
			423311,
			468831,
			467855,
			466002,
			463238,
			405685,
			421970,
			468052,
			427177,
			447878,
			463934,
			421715,
			448198,
			463959,
			415897,
			350189,
			415613,
			466216,
			420906,
			468924,
			398766,
			441583,
			467723,
			468005,
			463422,
			462237,
			416139,
			467206,
			466727,
			367428,
			376270,
			418585,
			463821,
			402012,
			468075,
			412880,
			468450,
			399474,
			419931,
			468179,
			394664,
			431307,
			438881,
			417524,
			447925,
			467536,
			426335,
			468441,
			420656,
			403964,
			421674,
			426193,
			467711,
			468930,
			466261,
			467243,
			391314,
			466354,
			425953,
			468681,
			467972,
			466613,
			468886,
			417078,
			415533,
			426684,
			455297,
			465712,
			443198,
			466073,
			464657,
			462885,
			467909,
			424130,
			468113,
			466054,
			467238,
			464073,
			468811,
			459810,
			465880,
			468071,
			432510,
			467055,
			468952,
			416099,
			465291,
			424995,
		];

		$end            = $page * $limit + $offset;
		$totalTeamCount = $this->getTotalCount();
		$this->info('Total team count : ' . $totalTeamCount);
		for ($i = 0; $i < $totalTeamCount; $i = $i + $limit) {
			if ($offset >= min($end, $totalTeamCount)) {
				break;
			}

			$rawTeams = $this->getData($limit, $offset);

			/** @var MemberRepository $memberRepo */

			foreach ($rawTeams as $rawTeam) {
				$vehicleCategory = "";
				$programName     = "";

				if (isset($rawTeam["program"]["programName"])) {
					$programName = $rawTeam["program"]["programName"];
				}
				//"2022 Uluslararası Efficiency Challenge Elektrikli Araç Yarışları Elektromobil Kategorisi"
				if (!str_contains($programName, "2022")) {
					continue;
				}

				$this->info('teamType:' . $programName);
				//  check international or high school
				if (str_contains($programName, "Lise") && !AppType::isLise()) {
					$this->warn('Teams is Lise BUT app INTERNATIONAL ');
					continue;
				} elseif (str_contains($programName, "Uluslararas") && AppType::isLise()) {
					$this->warn('Teams is INTERNATIONAL but app is LISE');
					continue;
				}

				if (!AppType::isLise()) {
					//  check vehicle category
					if (str_contains($programName, "Elektromobil")) {
						$vehicleCategory = VehicleCategory::ELECTROMOBILE;
					} elseif (str_contains($programName, "Hidromobil")) {
						$vehicleCategory = VehicleCategory::HYDROMOBILE;
					} else {
						$this->warn('Teams Type invalid' . $vehicleCategory);
						continue;
					}
				} else if (str_contains($programName, "Elektrikli")) {
					$vehicleCategory = VehicleCategory::ELECTROMOBILE;
				}

				if (!in_array($rawTeam["id"], $activeTeams)) {
					$this->warn('Teams skipped. Team is not active');
					continue;
				}

				$consultantIndex = $this->getTeamConsultant($rawTeam);

				$this->info('Importing team...' . $rawTeam["id"]);
				$teamData = [
					'team_id'           => $rawTeam["id"],
					'team_name'         => $rawTeam["applicantTeam"]["teamName"],
					'university_name'   => null, // kodlara karşılık gelen okul isimlerinin alınması gerek,
//					'vehicle_name' => null,
					'city_name'         => isset($rawTeam["applicant"]["profile"][0]["province"][0]["text"]) ? $rawTeam["applicant"]["profile"][0]["province"][0]["text"] : null,
					'team_leader'       => null,
					'team_leader_phone' => null,
					'team_leader_email' => null,
					'vehicle_category'  => $vehicleCategory,
					'team_member_count' => count($rawTeam["applicantTeam"]["members"]),
				];
				if (AppType::isLise()) {
					if (isset($rawTeam["applicantTeam"]["members"][0]["user"][0]["school"][0]["name"])) {
						$teamData['university_name'] = $rawTeam["applicantTeam"]["members"][0]["user"][0]["school"][0]["name"];
					}
				} else {
					if (isset($rawTeam["applicant"]["profile"][0]["school"][0]["name"])) {
						$teamData['university_name'] = $rawTeam["applicant"]["profile"][0]["school"][0]["name"];
					}
				}

				if (isset($rawTeam["applicant"])) {
//					$teamData['team_leader']       = $rawTeam["applicant"]["username"];
					$teamData['team_leader']       = $rawTeam["applicant"]["first_name"] . ' ' . $rawTeam["applicant"]["last_name"];
					$teamData['team_leader_phone'] = $rawTeam["applicant"]["profile"][0]["phoneNumber"];
					$teamData['team_leader_email'] = $rawTeam["applicant"]["email"];
				}

//				if ($consultantIndex != -1) {
//					$tempConsultant = $rawTeam["applicantTeam"]["members"][$consultantIndex]["user"][0];
//
//					$teamData['consultant_name']  = $tempConsultant["user"][0]["first_name"] . " " . $tempConsultant["user"][0]["last_name"];
//					$teamData['consultant_phone'] = $tempConsultant["phoneNumber"];
//					$teamData['consultant_email'] = $tempConsultant["email"];
//				}

				if ($team = $this->isRegistered($teamData["team_id"])) {
					$this->info('Updating team...' . $teamData["team_id"]);
//					$this->repo->setMainModel($team)->update($teamData); // TODO open
					$this->updateMembers($rawTeam["applicantTeam"]['members'], $team);
					continue;
				}
				$this->warn('====> Creating team...' . $teamData["team_id"]);
				$team = $this->repo->create($teamData);
				$this->addMembers($rawTeam["applicantTeam"]['members'], $team);
			}
			$offset += $limit;
		}

		return 0;
	}

	public function isRegistered($team_id)
	{
		return $this->repo->findByTeamNo($team_id);
	}

	public function getTeamConsultant($team)
	{
		foreach ($team["applicantTeam"]["members"] as $key => $member) {
			if ($member["memberType"][0]["id"] == 2) {
				return $key;
			}
		}

		return -1;
	}

	protected function getData($limit, $offset)
	{
		return $this->getDataFromAPI($limit, $offset);
//		return $this->getDataFromFile();
	}

	public function getTotalCount()
	{
		return $this->getDataFromAPI(1, 0, true);
	}

	protected function getDataFromFile()
	{
		$path = 'storage/app/response.json';

		return json_decode(file_get_contents($path), true);
	}

	protected function getDataFromAPI($limit, $offset, $forCount = false)
	{

		$path = 'storage/app/response.json';

		$response  = Http::withHeaders([
			'Content-Type' => 'application/json',
		])->post('https://bilimtoplum-pbs.tubitak.gov.tr/api/public/login', [
			"username" => "kys-rastmobile",
			"password" => "b5LeC!\$P-McE%dS"
		]);
		$authToken = $response->getHeaders()["Authorization"][0];

		$this->info('get teams data, offset:' . $offset);
		$response = Http::withHeaders([
			'Content-Type'  => 'application/json',
			'Authorization' => 'Bearer ' . $authToken,
		])->get("https://bilimtoplum-pbs.tubitak.gov.tr/api/kys/basvuru?limit={$limit}&offset={$offset}");
//		file_put_contents($path, json_encode($response->json()["results"]));

		if ($response->status() !== 200) {
			$this->error('HTTP ERROR: ' . $response->status());
			exit(1);
		}

		if ($forCount) {
			return $response->json()["count"];
		}
		$this->info("\tdata ready:");

		$json = $response->json();

		return isset($json["results"]) ? $json["results"] : [];
	}

	// TODO get all membertype and add this block
	private function findMemberType($type)
	{
		switch ($type) {
			case 1:
				return MemberType::Member;
			case 2:
				return MemberType::AcademicAdvisor;
			case 3:
				return MemberType::Captain;
			default :
				return MemberType::Member;
		}
	}

	private function addMembers($rawMembers, Team $team)
	{
		foreach ($rawMembers as $index => $rawMember) {

			$this->memberRepo->create([
				'first_name'      => $rawMember['user'][0]['user'][0]["first_name"],
				'last_name'       => $rawMember['user'][0]['user'][0]["last_name"],
				'identity_number' => $rawMember['user'][0]['tc'],
				'phone_number'    => $rawMember['user'][0]['phoneNumber'],
				'team_id'         => $team->id,
				'role_in_team'    => $this->findMemberType($rawMember['memberType'][0]["id"])
			]);
		}
	}

	private function updateMembers($rawMembers, Team $team)
	{
//		$rawMembers->pluck();
		$indentities = (new \Illuminate\Support\Collection($rawMembers))->pluck('user.0.tc')->toArray();
		$phones      = (new \Illuminate\Support\Collection($rawMembers))->pluck('user.0.phoneNumber')->toArray();

		$members = Member::where('team_id', $team->id)
			->whereNotIn('phone_number', $phones)
			->whereNotIn('identity_number', $indentities);

		if ($members->count()) {
			$this->error('deleted members: ' . $members->count());
			$members->delete();
		} else {
			$this->info('deleted members: ' . $members->count());
		}
		foreach ($rawMembers as $index => $rawMember) {
			if ($rawMember['user'][0]['tc']) {
				$member = $this->memberRepo->findMemberBy([
					'identity_number' => $rawMember['user'][0]['tc'],
					'team_id'         => $team->id,
				]);
			} else {
				$member = $this->memberRepo->findMemberBy([
					'phone_number' => $rawMember['user'][0]['phoneNumber'],
					'team_id'      => $team->id,
				]);
			}

			$memberData = [
				'first_name'      => $rawMember['user'][0]['user'][0]["first_name"],
				'last_name'       => $rawMember['user'][0]['user'][0]["last_name"],
				'identity_number' => $rawMember['user'][0]['tc'],
				'birthday'        => date('Y-m-d', strtotime($rawMember['user'][0]['birthday'])),
				'phone_number'    => $this->phoneNumber($rawMember['user'][0]['phoneNumber']),
				'role_in_team'    => $this->findMemberType($rawMember['memberType'][0]["id"]),
				'team_id'         => $team->id
			];

			if (isset($member)) {
				$memberData = array_filter($memberData);
				$diff       = array_diff($member->only(array_keys($memberData)), $memberData);
				$diff2      = array_diff($memberData, $member->only(array_keys($memberData)));
				if ($diff) {
					$this->info("\tMember change detected, updating...." . json_encode($diff) . ' - ' . json_encode($diff2));
					$this->memberRepo->setMainModel($member)->update($memberData);
				}
				continue;
			}
			$this->info("\tnew Member...");
			$this->memberRepo->create($memberData);
		}
	}

	protected function phoneNumber($phoneNumber)
	{
		if (!$phoneNumber) return $phoneNumber;

		if (substr($phoneNumber, 0, 1) === '0') {
			return substr($phoneNumber, 1);
		}
		if (substr($phoneNumber, 0, 3) === '+90') {
			return substr($phoneNumber, 3);
		}

		return $phoneNumber;
	}

}

