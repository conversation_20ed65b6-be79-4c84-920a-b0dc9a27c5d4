<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

/**
 * Class ClearData
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class ClearData extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'data:clear';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'create contest';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info('clearing...');

//		\DB::statement('TRUNCATE TABLE teams');
//		\DB::statement('TRUNCATE TABLE members');
		\DB::statement('TRUNCATE TABLE evaluations');
//		\DB::statement('TRUNCATE TABLE promotions');
//		\DB::statement('TRUNCATE TABLE penalties');
		\DB::statement('TRUNCATE TABLE appointments');
		\DB::statement('TRUNCATE TABLE evaluation_details');
		\DB::statement('TRUNCATE TABLE races');
		\DB::statement('TRUNCATE TABLE sessions');
		\DB::statement('TRUNCATE TABLE session_teams');
		\DB::statement('TRUNCATE TABLE user_assets');
		\DB::statement('TRUNCATE TABLE notifications');
		\DB::statement('TRUNCATE TABLE petition_likes');
		\DB::statement('TRUNCATE TABLE petitions');
		\DB::statement('TRUNCATE TABLE comment_likes');
		\DB::statement('TRUNCATE TABLE comments');

		$this->info('cleared');
	}
}
