<?php

namespace App\Console\Commands;

use App\Helpers\ReadInput;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Team;
use Illuminate\Console\Command;

/**
 * Class TeamIDMake
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class TeamIDMake extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'make:teamId';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'match teamId with name';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info('starting...');
		$this->info('enter tsv:');

		$input = ReadInput::readMultiLine();

		echo $input;

		$data = explode("\n", $input);
		$rows = [];

		$allTeams = app(TeamRepositoryInterface::class)->getAll();

		$teams = $allTeams->pluck('team_name')->toArray();
//		$teams = ["ÇAYDA-ARABA",
//			"E-GENERATION TECHNIC",
//			"TERME ELEKTRİKLİ ARAÇ",
//			"E-CARETTA",
//			"WOLFMOBİL",
//			"BOTEKO",
//			"ANKATECH",
//			"HAFIZ İHO STEAM EC",
//			"DENECAR",
//			"INTECHNE",
//			"ANAK",
//			"C.YEŞİL",
//			"THE GACA",
//			"DORLİON",
//			"GELECEĞİN YILDIZLARI",
//			"ETERNAL",
//			"YEŞİLYURT",
//			"BARAJ ELEKTRİKLİ ARABA",
//			"1919",
//			"T-CAR T3",
//			"MEGA SOLO",
//			"SİMURG",
//			"ESATAMAT",
//			"TEAM MOSTRA",
//			"ŞAHLAN",
//			"J-SOG",
//			"MİWİTHEM",
//			"NİF ROBOTİK",
//			"PARDUS",
//			"AZİM",
//			"AAATLAS",
//			"MUTEG EA",
//			"MOSTEMEC",
//			"AR-TEK 19",
//			"İZMİROBOTİK",
//			"AITT",
//			"V5 ZEKA",
//			"ALTAY",
//			"ALTAİR",
//			"SERİK E.A.",
//			"AROTO",
//			"ELLİ DÖRT",
//			"ÇAMELİ ELEKTROMOBİL",
//			"DEAM STUDİO",
//			"TULPAR",
//			"NÖTRİNO-88",
//			"AYKANAT",
//			"CEZERİ RÜZGARI",
//			"ASTREA",
//			"SCİ-TECH",
//			"KELROT",
//			"İSTİKLAL",
//			"KARINCALAR",];
		$found = 0;
		foreach ($data as $datum) {
			$row = explode("\t", $datum);
			if (isset($row[1]) && in_array(trim($row[1]), $teams)) {
				$index = array_search(trim($row[1]), $teams);
				unset($teams[$index]);
				$found++;
				continue;

				echo "found", "\t", $datum, " ";
			} else {
				echo "----", "\t", $datum;
			}
			echo "\n";
		}

		$this->info('OK');
		print_r($teams);

		echo 'found ', $found, "\n";
		echo 'fail ', count($teams), "\n";

		if (ReadInput::confirmInput()) {
			foreach ($data as $datum) {
				$row = explode("\t", $datum);
				if (!isset($row[1])) {
					continue;
				}
				echo $row[1], ' = ', $row[0], '...';

				Team::where('team_name', $row[1])->update([
					'team_id' => $row[0]
				]);
				echo "\n";
			}

			$this->info('SAVED');
			echo 'saved';

			return 0;
		}

		return 0;
	}
}
