<?php

namespace App\Console\Commands;

use App\Interfaces\TeamRepositoryInterface;
use <PERSON><PERSON><PERSON>;
use Http;
use Illuminate\Console\Command;

/**
 * Class AddTeamPhoto
 *
 * @package App\Console\Commands
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AddTeamPhoto extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'photo:team-add';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Add multi team photo';

	/**
	 * Execute the console command.
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function handle()
	{

		$this->info('clearing...');
		$teams = app(TeamRepositoryInterface::class)->getAll();

		$files = glob(storage_path("app/test/*.jpg"));

		foreach ($files as $index => $file) {
			$baseName = basename($file);
			$baseName = explode('.', $baseName)[0];
			$result   = $teams->filter(function ($item) use ($baseName) {
				$name = $item->university_name . ' ' . $item->team_name;

				return $name == $baseName || strtolower($name) == strtolower($baseName);
			});

			if ($result->count() > 0) {
				$teamId = $result->first()->id;

				echo basename($file), "\n";

				$response = Http::attach(
					'file', file_get_contents($file), 'file.jpeg'
				)->withHeaders([
					'Authorization' => 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9sb2NhbGhvc3Q6ODAwMFwvbG9naW4iLCJpYXQiOjE1OTgyNzc1MDcsImV4cCI6MTYwMTg3NzUwNywibmJmIjoxNTk4Mjc3NTA3LCJqdGkiOiIyWGt0ZnVkUmhjRTg5M3BCIiwic3ViIjoxLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.wfW9nqxNC6nDXdwEvVUqTfe7-Twa7dwH3aZ7Npw2-8k',
				])->post('http://api.tubitak.rastmobile.com/asset');

				$response->json()['id'];

				$response = Http::withHeaders([
					'Authorization' => 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9sb2NhbGhvc3Q6ODAwMFwvbG9naW4iLCJpYXQiOjE1OTgyNzc1MDcsImV4cCI6MTYwMTg3NzUwNywibmJmIjoxNTk4Mjc3NTA3LCJqdGkiOiIyWGt0ZnVkUmhjRTg5M3BCIiwic3ViIjoxLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.wfW9nqxNC6nDXdwEvVUqTfe7-Twa7dwH3aZ7Npw2-8k',
				])->post('http://api.tubitak.rastmobile.com/team/' . $teamId . '/attach', [
					"asset_id" => $response->json()['id'],
					"type"     => "team_photo"
				]);

				echo $response;
			} else {
				echo 'error ---- ' . $baseName, "\n";
			}
//			echo basename($file), "\n";
//			echo $file, "\n";
			$this->info('cleared');
		}
	}
}
