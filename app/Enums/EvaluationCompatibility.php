<?php
namespace App\Enums;

/**
 *
 */
abstract class EvaluationCompatibility extends Enum
{
	const EMPTY        = 0;
	const APPROVE      = 1;
	const DEFECTIVE    = 2;
	const DISQUALIFIED = 3;

	public static function getKeyName($index = null)
	{
		$keys = [
			self::EMPTY        => 'empty',
			self::APPROVE      => 'approve',
			self::DEFECTIVE    => 'defective',
			self::DISQUALIFIED => 'disqualified',
		];

		if (isset($index)) {
			return $keys[$index];
		}

		return $keys;
	}
}
