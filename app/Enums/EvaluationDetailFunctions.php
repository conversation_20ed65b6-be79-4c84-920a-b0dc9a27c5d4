<?php
namespace App\Enums;

/**
 *
 */
abstract class EvaluationDetailFunctions extends Enum
{
	const PENALTY = 'penalty';
	const AWARD   = 'award';

	const StaticPenalty  = 'static';
	const DynamicPenalty = 'dynamic';

	static $map = [
		'Ceza' => self::PENALTY,
		'Ödül' => self::AWARD,
	];


	static $penaltyTypes = [
		'Sabit' => self::StaticPenalty,
		'Değişken' => self::DynamicPenalty,
	];

	/**
	 * @param $string
	 * @return string|null
	 */
	public static function getTypes($string)
	{
		if (isset(static::$penaltyTypes[$string])) {
			return static::$penaltyTypes[$string];
		}

		return null;
	}

}
