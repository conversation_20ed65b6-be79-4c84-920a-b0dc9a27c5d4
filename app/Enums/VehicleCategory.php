<?php
namespace App\Enums;

/**
 * Class VehicleCategory
 *
 * @package App\Enums
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class VehicleCategory extends Enum
{
	const ELECTROMOBILE = 'electromobile';
	const HYDROMOBILE   = 'hydromobile';

	const EL_TR = 'elektromobil';
	const HY_TR = 'hidromobil';

	public static function isElectromobile($vehicleCategory)
	{
		return $vehicleCategory == static::ELECTROMOBILE;
	}
}
