<?php
namespace App\Enums;

/**
 * Class PenaltyType
 *
 * @package App\Enums
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class SessionTeamStatus extends Enum
{
	const FI  = 'FI';
	const DSQ = 'DSQ';
	const DNF = 'DNF';
	const DNS = 'DNS';

	public static function isScoreComputable($result)
	{
		if (!$result['valid_laps']) {
			return false;
		}

		if ($result['team']->vehicle_category == VehicleCategory::HYDROMOBILE) {
			if(!($result['total_cons'] && $result['total_hydrogen_cons'])){
				return false;
			}
			$Y = (3 * $result['total_hydrogen_cons']) / ($result['total_cons'] + 3 * $result['total_hydrogen_cons']);
			if (!($Y > 0.35 && $Y < 0.65)) { // tüketimine oranı %35 ile %65 aralığında olmalı
				return false;
			}
		}

		return !in_array($result['status'], [self::DNF, self::DNS, self::DSQ]);
	}
}
