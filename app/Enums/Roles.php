<?php
namespace App\Enums;

/**
 * Class Roles
 *
 * @package App\Enums
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class Roles extends Enum
{
	const SUPERADMIN       = 'superadmin';
	const USER             = 'user';
	const TEAM             = 'team';
	const APPOINTMENT      = 'appointment';
	const RACE             = 'race';
	const DDK              = 'ddk';
	const DDK_ALL          = 'ddk_all';
	const TEAM_CAPTAIN     = 'team_captain';
	const HES_CODE         = 'hes_code';
	const GIVE_STICKER     = 'give_sticker';
	const CLOSE_EVALUATION = 'close_evaluation';
	const GUIDE = 'guide';

//	const DDK_PHYSICAL_TEST             = 'DDK_Physical_Test';
//	const DDK_PHYSICAL_SPECIFICATION    = 'DDK_Physical_Specification';
//	const DDK_TEST                      = 'DDK_Test';
//	const DDK_HARDWARE                  = 'DDK_Hardware';
//	const DDK_SAFETY_HARDWARE           = 'DDK_Safety_Hardware';
//	const DDK_ELECTRICAL_SAFETY         = 'DDK_Electrical_Safety';
//	const DDK_BATTERY                   = 'DDK_Battery';
//	const DDK_MOTOR_DRIVER              = 'DDK_Motor_Driver';
//	const DDK_TELEMETRY                 = 'DDK_Telemetry';
//	const DDK_ELECTRICAL_TEST           = 'DDK_Electrical_Test';
//	const DDK_HYDROMOBILE               = 'DDK_Hydromobile';
//	const DDK_STICKER                   = 'DDK_Sticker';
//	const DDK_DOMESTIC_PARTS            = 'DDK_Domestic_Parts';
//	const DDK_VEHICLE_MANAGEMENT_SYSTEM = 'DDK_Vehicle_Management_System';
//	const DDK_AFTER_RACE_CHECKS         = 'DDK_After_Race_Checks';

	static $map = [
		"superadmin"   => self::SUPERADMIN,
		"user"         => self::USER,
		"ddk"          => self::DDK,
		"ddk_all"      => self::DDK_ALL,
		'team'         => self::TEAM,
		'appointment'  => self::APPOINTMENT,
		'race'         => self::RACE,
		'team_captain' => self::TEAM_CAPTAIN,
		'hes_code'     => self::HES_CODE,

//		'DDK_Physical_Test'             => self::DDK_PHYSICAL_TEST,
//		'DDK_Physical_Specification'    => self::DDK_PHYSICAL_SPECIFICATION,
//		'DDK_Test'                      => self::DDK_TEST,
//		'DDK_Hardware'                  => self::DDK_HARDWARE,
//		'DDK_Safety_Hardware'           => self::DDK_SAFETY_HARDWARE,
//		'DDK_Electrical_Safety'         => self::DDK_ELECTRICAL_SAFETY,
//		'DDK_Battery'                   => self::DDK_BATTERY,
//		'DDK_Motor_Driver'              => self::DDK_MOTOR_DRIVER,
//		'DDK_Telemetry'                 => self::DDK_TELEMETRY,
//		'DDK_Electrical_Test'           => self::DDK_ELECTRICAL_TEST,
//		'DDK_Hydromobile'               => self::DDK_HYDROMOBILE,
//		'DDK_Sticker'                   => self::DDK_STICKER,
//		'DDK_Domestic_Parts'            => self::DDK_DOMESTIC_PARTS,
//		'DDK_Vehicle_Management_System' => self::DDK_VEHICLE_MANAGEMENT_SYSTEM,
//		'DDK_After_Race_Checks'         => self::DDK_AFTER_RACE_CHECKS,
	];

	/**
	 * @return string
	 */
	public static function defaultRole()
	{
		return static::USER;
	}

	/**
	 * @return array
	 */
	public static function getBasicRoles()
	{
		$roles = static::getMapKeys();

		return array_diff($roles, [static::SUPERADMIN]);
	}
}
