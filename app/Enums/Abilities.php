<?php
namespace App\Enums;

/**
 * Class Abilities
 *
 * @package App\Enums
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class Abilities extends Enum
{
	const LIST   = 'list';
	const VIEW   = 'view';
	const CREATE = 'create';
	const UPDATE = 'update';
	const DELETE = 'delete';

	const IMPORT         = 'import';
	const EXPORT         = 'export';
	const APPROVE        = 'approve';
	const CHECK_HES_CODE = 'checkHesCode';
	const LIKE           = 'like';
	const COMMENT        = 'comment';

	/**
	 * @return array
	 */
	public static function withoutModels()
	{
		return [
			static::CREATE,
			static::LIST,
		];
	}

	/**
	 * @return array
	 */
	public static function withModels()
	{
		return [
			static::VIEW,
			static::UPDATE,
			static::DELETE,
		];
	}
}
