<?php
namespace App\Enums;

use ReflectionClass;

/**
 * Class Enum
 *
 * @package App\Enums
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class Enum
{
	/**
	 * @return array
	 */
	public static function getKeys()
	{
		$class = new ReflectionClass(get_called_class());

		return array_keys($class->getConstants());
	}

	/**
	 * @return array
	 */
	public static function getValues()
	{
		$class = new ReflectionClass(get_called_class());

		return array_values($class->getConstants());
	}

	public static function get($string)
	{
		if (isset(static::$map[$string])) {
			return static::$map[$string];
		}

		return null;
	}

	/**
	 * @return array
	 */
	public static function getMapKeys()
	{
		return array_keys(static::$map);
	}

	/**
	 * @param $enum
	 * @return |null
	 */
	public static function toHumanReadable($enum)
	{
		$map = array_flip(static::$map);

		return isset($map[$enum]) ? $map[$enum] : null;
	}
}
