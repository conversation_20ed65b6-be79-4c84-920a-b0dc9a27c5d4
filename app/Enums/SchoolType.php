<?php
namespace App\Enums;

/**
 * Class SchoolType
 *
 * @package App\Enums
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class SchoolType extends Enum
{
	const INTERNATIONAL = 1;
	const LISE          = 2;

	static $map = [
		"International" => self::INTERNATIONAL,
		'High School'   => self::LISE,
	];

	public static function isLiseTeam($team)
	{
		return $team->schoolType == static::LISE;
	}

}
