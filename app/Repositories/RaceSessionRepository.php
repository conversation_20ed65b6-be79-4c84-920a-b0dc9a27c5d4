<?php
namespace App\Repositories;

use App\Interfaces\EvaluationRepositoryInterface;
use App\Interfaces\RaceSessionRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Session;
use App\Models\SessionTeam;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Class RaceSessionRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class RaceSessionRepository extends BaseRepository implements RaceSessionRepositoryInterface
{
	use MainModelMethodsTrait {
		create as baseCreate;
	}
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	public function __construct(Session $mainModel)
	{
		parent::__construct($mainModel);
	}

//	public function create($data)
//	{
//		$session = $this->baseCreate($data);
//
//		$this->setMainModel($session)->addPossibleTeamsToSession();
//
//		return $session;
//	}

	public function addPossibleTeamsToSession()
	{
		$teams = app(TeamRepositoryInterface::class)
			->getTeamsWithSticker()
			->pluck('id')
			->toArray();

		return $this->addRaceTeam($teams);
	}

	public function findRaceTeam($id, $message = null)
	{
		$result = SessionTeam::find($id);
		if (!$result) {
			throw new NotFoundHttpException($message ?: 'resource.not_found');
		}

		return $result;
	}

	/**
	 * @param $teams
	 * @return \Illuminate\Support\Collection|SessionTeam[]
	 */
	public function addRaceTeam($teams)
	{
		if (!count($teams)) {
			return [];
		}
		$exists = $this->getMainModel()->raceTeams->pluck('team_id')->toArray();

		$teams = array_diff($teams, $exists);

		//TODO check team pass evaluation

		$collection = collect($teams)->map(function ($teamId) {
			return new SessionTeam(['team_id' => $teamId]);
		});


		$this->getMainModel()->raceTeams()
			->saveMany($collection);

		app(EvaluationRepositoryInterface::class)
			->createAfterRaceDetailsMulti($teams, $this->getMainModel()->id);

		return $collection;
	}

	/**
	 * @param SessionTeam $raceTeam
	 * @param             $data
	 * @return SessionTeam
	 */
	public function updateRaceTeam(SessionTeam $raceTeam, $data)
	{
		$raceTeam->start_point = $data['start_point'];

		$raceTeam->save();

		return $raceTeam;
	}
}
