<?php
namespace App\Repositories;

use App\Enums\AppType;
use App\Enums\VehicleCategory;
use App\Interfaces\TeamRepositoryInterface;
use App\Interfaces\TechnicalDesignRepositoryInterface;
use App\Models\TechnicalDesign;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Database\Eloquent\Builder;
use Str;
use Validator;

/**
 * Class TechnicalDesignRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method TechnicalDesign|Builder getMainModel()
 */
class TechnicalDesignRepository extends BaseRepository implements TechnicalDesignRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

//	protected $filterables = ['team_id'];

	public function __construct(TechnicalDesign $mainModel)
	{
		$this->defaults = ['limit' => 100];
		parent::__construct($mainModel);
	}

	/**
	 * @unused
	 * @param $collections
	 * @return array
	 */
	public function importOld($collections)
	{
		$errors = [];
		$rules  = [
			'no'       => 'nullable|integer',
			'takim_no' => 'nullable|integer',
			'toplam'   => 'nullable|numeric',

			'gr'                  => 'nullable|numeric',
			'gr_20'               => 'nullable|numeric',
			'gr_20_eklenmis_puan' => 'nullable|numeric',

			'arac_ozellikleri_tablosu'           => 'nullable|integer',
			'motor'                              => 'nullable|integer',
			'motor_surucusu'                     => 'nullable|integer',
			'batarya_yonetim_sistemi'            => 'nullable|integer',
			'yerlesik_sarj_birimi'               => 'nullable|integer',
			'batarya_paketleme'                  => 'nullable|integer',
			'elektronik_diferansiyel_uygulamasi' => 'nullable|integer',
			'arac_kontrol_sistemi'               => 'nullable|integer',
			'izolasyon_izleme_cihazi'            => 'nullable|integer',
			'direksiyon_sistemi'                 => 'nullable|integer',
			'kapi_mekanizmasi'                   => 'nullable|integer',
			'mekanik_detaylar'                   => 'nullable|integer',
			'arac_elektrik_semasi'               => 'nullable|integer',
			'orijinal_tasarim'                   => 'nullable|integer',
			'yakit_pili'                         => 'nullable|integer',
			'yakit_pili_kontrol_sistemi'         => 'nullable|integer',
			'enerji_yonetim_sistemi'             => 'nullable|integer',

			'dinamik_surus_testi' => 'nullable|integer',
			'telemetri'           => 'nullable|integer',
			'yerlilik_durumu'     => 'nullable|boolean',

		];

		$map     = [
			'takim_no' => 'takim_id',
			'toplam'   => 'toplam_puan',

			'gr'                  => 'gr',
			'gr_20'               => 'gr_20',
			'gr_20_eklenmis_puan' => 'gr_20_eklenmis_puan',

			'arac_ozellikleri_tablosu'           => 'arac_ozellikleri_tablosu_puan',
			'motor'                              => 'motor_puan',
			'motor_surucusu'                     => 'motor_surucusu_puan',
			'batarya_yonetim_sistemi'            => 'batarya_yonetim_sistemi_puan',
			'yerlesik_sarj_birimi'               => 'yerlesik_sarj_birimi_puan',
			'batarya_paketleme'                  => 'batarya_paketleme_puan',
			'elektronik_diferansiyel_uygulamasi' => 'elektronik_diferansiyel_uygulamasi_puan',
			'arac_kontrol_sistemi'               => 'arac_kontrol_sistemi_puan',
			'izolasyon_izleme_cihazi'            => 'izolasyon_izleme_cihazi_puan',
			'direksiyon_sistemi'                 => 'direksiyon_sistemi_puan',
			'kapi_mekanizmasi'                   => 'kapi_mekanizmasi_puan',
			'mekanik_detaylar'                   => 'mekanik_detaylar_puan',
			'arac_elektrik_semasi'               => 'arac_elektrik_semasi_puan',
			'orijinal_tasarim'                   => 'orijinal_tasarim_puan',
			'yakit_pili'                         => 'yakit_pili_puan',
			'yakit_pili_kontrol_sistemi'         => 'yakit_pili_kontrol_sistemi_puan',
			'enerji_yonetim_sistemi'             => 'enerji_yonetim_sistemi_puan',
			'dinamik_surus_testi'                => 'dinamik_surus_testi_puan',
			'telemetri'                          => 'telemetri_puan',
			'yerlilik_durumu'                    => 'yerlilik_durumu',

			'orj_arac_ozellikleri_tablosu'           => 'arac_ozellikleri_tablosu_puan',
			'orj_motor'                              => 'motor_puan',
			'orj_motor_surucusu'                     => 'motor_surucusu_puan',
			'orj_batarya_yonetim_sistemi'            => 'batarya_yonetim_sistemi_puan',
			'orj_yerlesik_sarj_birimi'               => 'yerlesik_sarj_birimi_puan',
			'orj_batarya_paketleme'                  => 'batarya_paketleme_puan',
			'orj_elektronik_diferansiyel_uygulamasi' => 'elektronik_diferansiyel_uygulamasi_puan',
			'orj_arac_kontrol_sistemi'               => 'arac_kontrol_sistemi_puan',
			'orj_izolasyon_izleme_cihazi'            => 'izolasyon_izleme_cihazi_puan',
			'orj_direksiyon_sistemi'                 => 'direksiyon_sistemi_puan',
			'orj_kapi_mekanizmasi'                   => 'kapi_mekanizmasi_puan',
			'orj_mekanik_detaylar'                   => 'mekanik_detaylar_puan',
			'orj_arac_elektrik_semasi'               => 'arac_elektrik_semasi_puan',
			'orj_orijinal_tasarim'                   => 'orijinal_tasarim_puan',
			'orj_yakit_pili'                         => 'yakit_pili_puan',
			'orj_yakit_pili_kontrol_sistemi'         => 'yakit_pili_kontrol_sistemi_puan',
			'orj_enerji_yonetim_sistemi'             => 'enerji_yonetim_sistemi_puan',
			'orj_dinamik_surus_testi'                => 'dinamik_surus_testi_puan',
			'orj_telemetri'                          => 'telemetri_puan',

		];
		$imports = [];
		foreach ($collections as $collection) {
			foreach ($collection as $i => $row) {
				$data = [];

				foreach ($map as $index => $item) {
					$data[$index] = $row->has($item) ? $row[$item] : null;
				}

				$validator = Validator::make($data, $rules);

				if ($validator->fails()) {
					$bag = $validator->errors();
					$bag->add('satir', 'satir ' . $i);
					throw new ResourceException("validation.error", $bag);
//				$errors[] = ['errors' => $validator->errors(), 'data' => $data];
//				continue;
				}
				if (!isset($data['takim_no'])) {
					continue;
				}

				$imports[] = $data;
			}
		}

//		if (count($errors) > 0) {
//			throw new ResourceException("validation.error", $errors);
//		}

		TechnicalDesign::insert($imports);

		return $errors;
	}

	//=REGEXREPLACE(REGEXREPLACE(C2,"^\d+[\.\s]",""),"\((0-\d+)\sPuan\)","Puan ($1)")
	public function import($collection)
	{
		$commonColumns = [
			'Araç Elektrik Şeması',
			'Araç Kontrol Sistemi',
			'Araç Özellik Tablosu',
			'Batarya Paketleme',
			'Batarya Yönetim Sistemi',
			'Direksiyon Sistemi',
			'Elektronik Diferansiyel Uygulaması',
			'Fren Sistemi',
			'İzolasyon İzleme Cihazı',
			'Kapı Mekanizması',
			'Mekanik Detaylar',
			'Motor Sürücüsü',
			'Motor',
//2024 icin kaldirilanlar
//			'Orijinal Tasarım',
//			'Tanıtım ve Yaygınlaştırma Raporu',
//			'Telemetri Sistemi',
			'Yerleşik Şarj Birimi',
			'Yolcu Koltuğu',
			'Rapor Düzeni',
		];

//		if (AppType::isLise()) {
//			$commonColumns = [
//			];
//		}

		$hydromobileColumns = [
			'Enerji Yönetim Sistemi',
//			'Hidrojen Sistemi', 2024 icin kaldirildi

			'Yakıt Hücresi',
			'Yakıt Hücresi Kontrol Sistemi',
			'Hidrojen Hattı ve Metal Hidrür Silindirler',
		];

		$teamsMap   = app(TeamRepositoryInterface::class)->getAll()->keyBy('team_id');
		$rows       = [];
		$allRows    = [];
		foreach ($collection as $index => $item) {
			$teamNo = (int)$item->get('TAKIM ID');
			if (!$teamNo) {
				continue;
			}
			$team = $teamsMap->get($teamNo);
			if (!$team) {
				throw new ResourceException('Team not found', [
					'teamNo' => $teamNo,
				]);
			}
			$columns = $commonColumns;
			if (!AppType::isLise() && !VehicleCategory::isElectromobile($team->vehicle_category)) {
				$columns = array_merge($columns, $hydromobileColumns);
			}
			foreach ($columns as $column) {
				$columnGroups = $item->keys()->filter(function ($i) use ($column) {
					if ($column == 'Motor') {
						return Str::startsWith($i, $column) && !Str::contains($i, 'Sürücüsü');
					}

					return Str::startsWith($i, $column);
				});

				$pointKey    = $columnGroups->first(function ($i) use ($column) {
					return Str::contains($i, 'Puan (');
				});
				$commentKey  = $columnGroups->first(function ($i) use ($column) {
					return Str::contains($i, 'Yorum');
				});
				$domesticKey = $columnGroups->first(function ($i) use ($column) {
					return Str::contains($i, 'Yerlilik');
				});
				$copyKey     = $columnGroups->first(function ($i) use ($column) {
					return Str::contains($i, 'Kopya/İntihal');
				});
				$videoKey    = $columnGroups->first(function ($i) use ($column) {
					return Str::contains($i, 'Teknik Kontrol Videosu');
				});

				/* Video puanlar 2024'de kaldirildi
				 * 				if ($item->get($videoKey)) {
									$rows[] = [
										'team_id'        => $team ? $team->id : null,
										'team_no'        => $teamNo,
										'key'            => str_replace("_x000D_\n", ' ', $videoKey),
										'point'          => (int)$item->get($videoKey),
										'original_point' => (int)$item->get($videoKey),
										'copy'           => -1, //todo
										'domestic'       => -1,
										'comment'        => '',
									];
								}*/

//				$key  = $item[1];
//				if (\Str::endsWith($key, '*')) {
//					if ($team && $team->vehicle_category != VehicleCategory::HYDROMOBILE) {
//						continue;
//					}
//					$key = \Str::replaceLast('*', '', $key);
//				}
				$rows[] = [
					'team_id'        => $team ? $team->id : null,
					'team_no'        => $teamNo,
					'key'            => $column,
					'point'          => (int)$item->get($pointKey),
					'original_point' => (int)$item->get($pointKey),
					'copy'           => !$copyKey ? -1 : (Str::contains($item->get($copyKey), 'Var') ? 1 : 0), //todo
					'domestic'       => !$domesticKey ? -1 : (
					Str::contains($item->get($domesticKey), 'Var') || Str::contains($item->get($domesticKey), 'Evet') ? 1 : 0),
					'comment'        => mb_substr($item->get($commentKey), 0, 1024),
				];

			}
			$this->getMainModel()->insert($rows);
//			foreach ($rows as $index => $row) {
//				$this->getMainModel()->insert([$row]);
//			}
			$allRows = array_merge($allRows, $rows);
			$rows    = [];
			$this->calculateTTRTotal($team);
		}

//		return $this->getMainModel()->insert($rows);
		return $allRows;
	}

	public function calculateTTRTotal($team)
	{
		$this->getMainModel()->where([
			'team_no' => $team->team_id,
			'key'     => 'Toplam'
		])->delete();

		$point = $this->getMainModel()->where([
			'team_no' => $team->team_id,
		])
			->where('key', 'not like', '%Teknik Kontrol Videosu%')
			->sum('point');

		$this->getMainModel()->create([
			'team_no'        => $team->team_id,
			'team_id'        => $team->id,
			'point'          => $point,
			'original_point' => $point,
			'key'            => 'Toplam',
			'copy'           => -1,
			'domestic'       => -1,
		]);
	}

	public function update($data)
	{
		$this->getMainModel()->update($data);

		$team = $this->getMainModel()->team;
		$this->calculateTTRTotal($team);

		return $data;
	}

	/**
	 * @param $teamId
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection
	 */
	public function getByTeam($teamId)
	{
		return $this->getMainModel()->where('team_id', $teamId)->get();
	}

	/**
	 * @unused
	 * @param $data
	 * @return bool|int
	 */
	public function updateOld($data)
	{
		$result = $this->getMainModel()->update($data);

		$arr = $this->getMainModel()->only([
			'arac_ozellikleri_tablosu',
			'motor',
			'motor_surucusu',
			'batarya_yonetim_sistemi',
			'yerlesik_sarj_birimi',
			'batarya_paketleme',
			'elektronik_diferansiyel_uygulamasi',
			'arac_kontrol_sistemi',
			'izolasyon_izleme_cihazi',
			'direksiyon_sistemi',
			'kapi_mekanizmasi',
			'mekanik_detaylar',
			'arac_elektrik_semasi',
			'orijinal_tasarim',
			'yakit_pili',
			'yakit_pili_kontrol_sistemi',
			'enerji_yonetim_sistemi',
			'dinamik_surus_testi',
			'telemetri',
			'yerlilik_durumu',
		]);

		$sum = array_sum($arr);
		if ($this->getMainModel()->takim_no == '57086') {
			$sum = $sum * 0.94; // TODO ozel bir kosul istendi sum - sum * 0.02 * 3
		}

		$sumWithGR20 = $this->getMainModel()->gr_20 + $sum;

		$this->getMainModel()->update([
			'toplam'              => $sum,
			'gr_20_eklenmis_puan' => $sumWithGR20
		]);

		return $result;
	}

	public function truncate()
	{
		\DB::statement('TRUNCATE TABLE ' . $this->getMainModel()->getTable());
	}

}
