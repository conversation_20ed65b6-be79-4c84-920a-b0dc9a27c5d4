<?php

	namespace App\Repositories;

	use App\Enums\Roles;
	use App\Interfaces\AssetRepositoryInterface;
	use App\Interfaces\UserDetailRepositoryInterface;
	use App\Interfaces\UserRepositoryInterface;
	use App\Models\User;
	use App\Models\UserAsset;
	use App\Traits\Repository\CreateMainModel;
	use App\Traits\Repository\FindMainModel;
	use App\Traits\Repository\FindOrFailMainModel;
	use App\Traits\Repository\GetAllMainModel;
	use App\Traits\Repository\UpdateMainModel;
	use App\Traits\ValidateTrait;
	use Bouncer;
	use Dingo\Api\Exception\ResourceException;
	use Exception;
	use Hash;
	use Illuminate\Database\Eloquent\Builder;
	use Illuminate\Database\Eloquent\Model;
	use Illuminate\Support\Str;

	/**
	 * Class UserRepository
	 *
	 * @package App\Repositories
	 * <AUTHOR> <<EMAIL>>
	 * @version 0.1
	 * @since   0.1
	 * @method User|Builder getMainModel()
	 */
	class UserRepository extends BaseRepository implements UserRepositoryInterface
	{
		use UpdateMainModel;
		use CreateMainModel;
		use GetAllMainModel;
		use FindMainModel;
		use FindOrFailMainModel;
		use ValidateTrait;

		/**
		 * @var UserDetailRepositoryInterface
		 */
		protected $userDetailRepo;

		/**
		 * @return UserDetailRepositoryInterface
		 */
		public function getUserDetailRepo(): UserDetailRepositoryInterface
		{
			return $this->userDetailRepo;
		}

		/**
		 * @param Model $mainModel
		 * @return UserRepositoryInterface|BaseRepository
		 */
		public function setMainModel(Model $mainModel)
		{
			$this->getUserDetailRepo()->setUser($mainModel);

			return parent::setMainModel($mainModel);
		}

		/**
		 * UserRepository constructor.
		 */
		public function __construct(User $mainModel, UserDetailRepositoryInterface $userSettingRepo)
		{
			$this->userDetailRepo = $userSettingRepo;
			parent::__construct($mainModel);
		}

		/**
		 * @inheritDoc
		 */
		public function createUser($userData, $userDetail = []): User
		{
			$role = $userData['role'] ?? Roles::defaultRole();
			unset($userData['role']);

			$pictureId = null;
			if (isset($userData['picture_id'])) {
				$pictureId = $userData['picture_id'];
				unset($userData['picture_id']);
			}
			if (isset($userData['password'])) {
				$userData['password'] = Hash::make($userData['password']);
			}

			/** @var User $user */
			$user = $this->create($userData);
			$this->setMainModel($user);
			$this->assignRole($role);
			$pictureId && $this->assignProfilePicture($pictureId);

			$this->userDetailRepo
				->setUser($user)
				->createUserDetails($userDetail);

			return $user;
		}

		/**
		 * @inheritDoc
		 */
		public function updateUser($userData, $userDetail = null)
		{
			if (isset($userData['role'])) {
				$this->setRoles($userData['role']);
				unset($userData['role']);
			}
			if (isset($userData['picture_id'])) {
				$this->assignProfilePicture($userData['picture_id']);
				unset($userData['picture_id']);
			}
			if (isset($userData['password'])) {
				$userData['password'] = Hash::make($userData['password']);
			}

			$this->update($userData);
			$this->getUserDetailRepo()->updateUserDetails($userDetail);

			return $this->getMainModel();
		}

		/**
		 * @inheritDoc
		 */
		public function getUserByEmail($email)
		{
			return $this->getMainModel()->where('email', $email)->first();
		}

		/**
		 * @inheritDoc
		 */
		public function assignRole($roles)
		{

			if (is_string($roles)) {
				$roles = [$roles];
			}

			if (!is_array($roles) || empty($roles)) {
				$roles = [];
			}

			if (array_filter($roles, static function ($item) {
				return Str::startsWith($item, 'DDK_');
			})) {
				$roles[] = Roles::DDK;
				$roles = array_unique($roles);
			}

			return $this->getMainModel()->assign($roles);
		}

		public function setRoles($roles)
		{
			$this->getMainModel()->roles()->detach();

			return $this->getMainModel()->assign($roles);
		}

		/**
		 * @inheritDoc
		 * @throws Exception
		 */
		public function removeUser(User $user)
		{
			$user->details()->delete();
			$user->delete();

			return $user;
		}

		/**
		 * @return bool
		 */
		public function isProfileCompleted()
		{
			$required = [
//			'username' => 'required',
				'password' => 'required',
			];

			try {
				$this->validate($required, $this->getMainModel()->getAttributes());
			} catch (ResourceException $e) {
				foreach ($e->getErrors()->getMessageBag()->getMessages() as $key => $message) {
					return 'pick_' . $key;
				}

				return false;
			}

			return true;
		}

		/**
		 * @param $assetId
		 * @return false|Model
		 */
		public function assignProfilePicture($assetId)
		{
			/** @var AssetRepositoryInterface $assetRepo */
			$assetRepo = app(AssetRepositoryInterface::class)->setUser($this->getMainModel());
			$asset = $assetRepo->findUserAssetOrFail($assetId);

			return $this->getMainModel()->picture()->save(new UserAsset([
				'asset_id' => $asset->id,
			]));
		}

		public function getAllRoles()
		{
			return Bouncer::role()->all();
		}
	}
