<?php
namespace App\Repositories;

use App\Enums\AppType;
use App\Enums\SchoolType;
use App\Enums\SessionTeamStatus;
use App\Enums\VehicleCategory;
use App\Interfaces\SessionTeamRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Race;
use App\Models\SessionTeam;
use App\Traits\FiltersTrait;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class SessionTeamRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Race|Builder getMainModel()
 */
class SessionTeamRepository extends BaseRepository implements SessionTeamRepositoryInterface
{
	use FiltersTrait;
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	protected $filterables = ['session_id', 'team_id'];

	public function __construct(SessionTeam $mainModel)
	{
		$this->defaults['sort'] = ['score:desc'];
		parent::__construct($mainModel);
	}

	/**
	 * @param $result
	 * @return mixed
	 */
	public function afterUpdate($result)
	{
		$team = app(TeamRepositoryInterface::class)->findOrFail($result->team_id);
//		if ($result->status != SessionTeamStatus::FI) {
//			return $result;
//		}

		$result['total_cons']          = $result['last_energy_cons'] - $result['initial_energy_cons'];
		$result['total_hydrogen_cons'] = $result['last_hydrogen_cons'] - $result['initial_hydrogen_cons'];
		$result['final']               = $result['total_cons'] + $result['penalty'];

		$score = null;
		if (SessionTeamStatus::isScoreComputable($result)) {
			$score = $this->calculateScore($result, $team);
		}

		if (isset($score) && $team->vehicle_category == VehicleCategory::HYDROMOBILE) {
			$abs   = abs($result['total_cons'] - 3 * $result['total_hydrogen_cons']);
			$score = $score - $abs - $result['total_hydrogen_cons'];
		}

		$result['score'] = isset($score) ? round($score, 2) : null;

		$result->save();

		return $result;
	}

	protected function calculateScore2020($result)
	{
		return 150
			- ($result['total_cons'] / $result['valid_laps'])
			- ((30 - $result['valid_laps']) * 5)
			- $result['penalty'];
	}

	protected function calculateScore($result, $team)
	{
//		return SchoolType::isLiseTeam($team) ? //2024 icin kaldirildi
//			$this->calculateScoreForHighSchool($result) : $this->calculateScoreForInternational($result);

		return $this->calculateScoreForInternational($result);
	}

	/*protected function calculateScoreForInternational2022($result)
	{
		return $result['valid_laps'] * 5
			- ($result['total_cons'] / $result['valid_laps'])
			- $result['penalty'];
	}*/

	protected function calculateScoreForInternational($result)
	{
		return 1000	- $result['total_cons'] - $result['penalty'];
	}

	protected function calculateScoreForHighSchool($result)
	{
		return $result['valid_laps'] * 50
			- 3 * ($result['total_cons'] / $result['valid_laps'])
			- $result['penalty'];
	}

	public function getByEvent($teamId, $sessionId = null)
	{
		$query = $this->getMainModel()->where('team_id', $teamId);

		if ($sessionId) {
			$query->where('session_id', $sessionId);
		}

		return $query->get();
	}

	public function addedPenaltyEvent($teamId, $sessionId = null)
	{
		foreach ($this->getByEvent($teamId, $sessionId) as $sessionTeam) {
			$this->afterUpdate($sessionTeam);
		}
	}

}
