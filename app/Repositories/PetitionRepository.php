<?php

namespace App\Repositories;

use App\Interfaces\FormRepositoryInterface;
use App\Interfaces\PetitionRepositoryInterface;
use App\Models\Form;
use App\Models\Petition;
use App\Models\UserAsset;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use App\Traits\UserTrait;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class PetitionRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Petition|Builder getMainModel()
 */
class PetitionRepository extends BaseRepository implements PetitionRepositoryInterface
{
	use MainModelMethodsTrait {
		create as baseCreate;
		update as baseUpdate;
	}
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;
	use UserTrait;




	public function __construct(Petition $mainModel)
	{
		parent::__construct($mainModel);
		$this->defaults       = ['limit' => 100];
	}

	/**
	 * @param $data
	 * @return Builder|\Illuminate\Database\Eloquent\Model
	 */
	public function create($data)
	{
		$attachments = null;
		if (array_key_exists('attachments', $data)) {
			$attachments = $data['attachments'];
			unset($data['attachments']);
		}

		$form = $this->baseCreate($data);
		$this->setMainModel($form);

		$this->assignAttachment($attachments);

		return $form;
	}

	protected function assignAttachment($attachments)
	{
		foreach ((array)$attachments as $index => $attachment) {
			$this->getMainModel()->attachments()->save(new UserAsset([
				'asset_id' => $attachment['asset_id'],
				'type'     => $attachment['type'],
			]));
		}

	}

	public function update($data)
	{
		$attachments = null;
		if (array_key_exists('attachments', $data)) {
			$attachments = $data['attachments'];
			unset($data['attachments']);
		}

		$form = $this->baseUpdate($data);

		$this->assignAttachment($attachments);
		return $form;

	}


	/**
	 * @inheritDoc
	 */
	public function like()
	{
		return $this->getUser()->likePetition($this->getMainModel());
	}

	/**
	 * @inheritDoc
	 */
	public function dislike()
	{
		return $this->getUser()->dislikePetition($this->getMainModel());
	}
	/**
	 * @inheritDoc
	 */
	public function hesitant()
	{
		return $this->getUser()->hesitantPetition($this->getMainModel());
	}

}
