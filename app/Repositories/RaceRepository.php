<?php
namespace App\Repositories;

use App\Interfaces\RaceRepositoryInterface;
use App\Models\Race;
use App\Models\SessionTeam;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class RaceRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Race|Builder getMainModel()
 */
class RaceRepository extends BaseRepository implements RaceRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	public function __construct(Race $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function calculateBestScore()
	{
		$race = $this->getMainModel();

		$sessionIds = $race->sessions->pluck('id');

		$scoreQuery = SessionTeam::whereIn('session_id', $sessionIds)
			->orderBy('score', 'desc');

		$scoreQuery->update([
			'best' => false
		]);
		$scores = $scoreQuery->get();

		$teamScore = [];
		foreach ($scores as $score) {
			if (!isset($teamScore[$score->team_id])) {
				$teamScore[$score->team_id] = $score;
				$score->best                = true;
				$score->save();
			}
		}
	}

	public function endRace()
	{
		$this->calculateBestScore();

		$race        = $this->getMainModel();
		$race->ended = true;
		$race->save();
	}

}
