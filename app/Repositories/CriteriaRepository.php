<?php
namespace App\Repositories;

use App\Enums\AppType;
use App\Enums\CriteriaCategory;
use App\Enums\CriteriaType;
use App\Enums\EvaluationCompatibility;
use App\Helpers\EvalHelper;
use App\Interfaces\CriteriaRepositoryInterface;
use App\Models\Criteria;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Dingo\Api\Exception\ResourceException;

/**
 * Class CriteriaRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CriteriaRepository extends BaseRepository implements CriteriaRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	protected $filterables = [];

	public function __construct(Criteria $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function getCriterias()
	{
		return $this->getMainModel()->whereNotIn('category', [
			CriteriaCategory::After_Race_Checks,
			CriteriaCategory::Yaris_Sonrasi_kontroller
		])->get();
	}

	public function getAfterRaceCriterias()
	{
		return $this->getMainModel()->whereIn('category', [
			CriteriaCategory::After_Race_Checks,
			CriteriaCategory::Yaris_Sonrasi_kontroller
		])->get();
	}

	public function truncate()
	{
		\DB::statement('TRUNCATE TABLE criterias');
	}

	public function getAllCategories()
	{
		return $this->getMainModel()
			->select(['category', 'sub_category'])
			->distinct()
			->get();
	}

	/**
	 * #13.4 != UYGUN veya #13.5 != UYGUN veya #13.6 != UYGUN ise
	 * COUNT([#2.7, #6.4, #9.8, #13.7], UYGUN) < 2
	 *
	 * @param string $content
	 * @return bool
	 */
	protected function checkHasRule(string $content)
	{
		return preg_match('/^((\s*#\d+\.\d+\s*\!?=\s*"?(UYGUN|KUSURLU)"?\s*)(veya|ve)?)+(ise)*$/', $content) ||
			preg_match('/COUNT\(.+,\s*(UYGUN|KUSURLU)\)/', $content);
	}

	protected function buildRule($rule)
	{
		$matches = [];
		preg_match_all('/#(\d+\.\d+)/', $rule, $matches);

		$rule = str_replace(
			['COUNT', 'ise', 'KUSURLU', 'UYGUN', 'veya', 've', '='],
			['criteriaCount', '', EvaluationCompatibility::DEFECTIVE, EvaluationCompatibility::APPROVE, 'or', 'and', '==',], $rule);

		return $rule;
	}

	protected function checkFormulas(string $content = null)
	{
		if (!isset($content)) {
			return true;
		}
		$content = trim($content);

		if ($content == '+') {
			return true;
		}
		$parsed = EvalHelper::parseFunction(trim($content));
		if (!$parsed) {
			return false;
		}
		if (!$parsed->function) {
			return false;
		}

		return true;
	}

	public function import($importedRows)
	{

		$category      = null;
		$subject       = null;
		$rows          = [];
		$i             = 0;
		$afterRaceRows = [];
		foreach ($importedRows as $item) {
			$i++;
			if (!(isset($item[0]) || isset($item[1]) || isset($item[2])) || $item[0] === 'Sekme') {
				continue;
			}
			if (isset($item[0])) {
				$category = trim($item[0]);
			}
			if (isset($item[1])) {
				$subCategory = trim($item[1]);
			}
			if (isset($item[4])) {
				$subject = trim($item[4]);
			}
			$content = trim($item[5]);
			$rawRule = trim($item[6]);

			$key = $item[3] . '-' . $category . '-' . $subCategory . '-' . $subject . '-' . $content;

			if (!isset($item[5])) {
				if (isset($item[1]) || isset($item[3])) {
					$item[5] = $item[4] ?: $item[2];
				} else {
					continue;
				}
			}

			// allow_ validation
			if (!$this->checkRuleNumber($item[2])) {
				throw new ResourceException('Rule number is invalid', [
					'row'    => $i,
					'rawRow' => $item,
				]);
			}
			// allow_ validation
			if (!isset($item[9]) && !isset($item[10]) && !isset($item[11])) {
				throw new ResourceException('at least allow one choose(DİSKALİFİYE,KUSURLU,UYGUN):', [
					'row'    => $i,
					'rawRow' => $item,
				]);
			}

			//rule parsing
			$rule = null;
			if ($rawRule) {
				if ($this->checkHasRule($rawRule)) {
					$rule = $this->buildRule($rawRule);
				} else {
					throw new ResourceException('invalid row rule:', [
						'row'     => $item,
						'rawRule' => $rawRule
					]);
				}
			}

			//active column parsing
			$active = $item[7] ?: '0';
			if (strlen($active) !== 1) {
				if ($this->checkHasRule($active)) {
					$active = $this->buildRule($active);
				} else {
					throw new ResourceException('invalid active rule:', [
						'row'         => $item,
						'active_rule' => $active
					]);
				}
			}

			if (isset($item[9]) || isset($item[10]) || isset($item[11])) {
				if (!($this->checkFormulas($item[9]) &&
					$this->checkFormulas($item[10]) &&
					$this->checkFormulas($item[11]))) {
					throw new ResourceException('invalid penalty/prize formula', [
						'row'      => $item,
						'formulas' => [$item[9], $item[10], $item[11]]
					]);
				}
			}

			if (!isset($rows[$key])) {
				$rows[$key] = [
					'category'     => $category,
					'sub_category' => $subCategory,
					'no'           => trim($item[2]),
					'subject'      => $subject,
					'raw_rule'     => $rawRule,
					'rule'         => $rule,
					'type'         => $item[3] == 'H' ? CriteriaType::HYDROMOBILE : ($item[3] == 'E' ? CriteriaType::ELECTROMOBILE : null),

					'content'            => $content,
					'is_required'        => isset($item[8]) ? $item[8] == '*' : false,
					'active'             => $active,
					'allow_value'        => true, // TODO false
					'allow_disqualified' => isset($item[9]) ? trim($item[9]) : false,
					'allow_defective'    => isset($item[10]) ? trim($item[10]) : false,
					'allow_approve'      => isset($item[11]) ? trim($item[11]) : false,
					'penalty_condition'  => $content && $content[0] == '#' ? $content : null
				];
				if (isset($item[12]) && $item[12]) {
					$afterRaceRows[$key . '_after'] = $this->convertAfterRaceRow($rows[$key]);
				}
			} else {
				throw new ResourceException('Duplicate rule', [
					'key'      => $key,
					'row'      => $item,
				]);
			}
		}
		$rows = array_merge($rows, $afterRaceRows);

		return $rows;
	}

	protected function convertAfterRaceRow(array $row)
	{
		$orjNo             = $row['no'];
		$row['subject']    = $row['category'] . "\n" . $row['subject'];
		$row['category']   = CriteriaCategory::After_Race_Checks;
		$row['after_race'] = true;
		$row['no']         = 'AR' . $row['no'];
		if ($row['rule']) {
			$row['rule']     = str_replace('#', '#AR', $row['rule']);
			$row['raw_rule'] = str_replace('#', '#AR', $row['raw_rule']);
		}

		if (EvalHelper::parseFunction($row['allow_disqualified'])->function) {
			$row['allow_disqualified'] = str_replace('Wh', 'Wh;!#' . $orjNo, $row['allow_disqualified']);
		}
		if (EvalHelper::parseFunction($row['allow_defective'])->function) {
			$row['allow_defective'] = str_replace('Wh', 'Wh;!#' . $orjNo, $row['allow_defective']);
		}
		if (EvalHelper::parseFunction($row['allow_approve'])->function) {
			$row['allow_approve'] = str_replace('Wh', 'Wh;!#' . $orjNo, $row['allow_approve']);
		}

//		$row['rule']       = null;
//		$row['raw_rule']   = null;
//		$row['active']     = 1;

//		$row['rule']       = str_replace('#', '#AR', $row['rule']);
//		$row['raw_rule']   = str_replace('#', '#AR', $row['raw_rule']);

//		$row['rule']       = preg_replace('/#(\d+\.\d+)/','#AR$1',$row['no']);

		return $row;
	}

	protected function checkRuleNumber($content)
	{
		return preg_match('/^\d+\.\d+$/', $content);
	}

}
