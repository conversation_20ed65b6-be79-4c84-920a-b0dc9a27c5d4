<?php
namespace App\Repositories;

use App\Interfaces\NotificationRepositoryInterface;
use App\Traits\UserTrait;
use Illuminate\Notifications\DatabaseNotification;

/**
 * Class NotificationRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class NotificationRepository extends BaseRepository implements NotificationRepositoryInterface
{
	use UserTrait;

	/**
	 * NotificationRepository constructor.
	 *
	 * @param DatabaseNotification $mainModel
	 */
	public function __construct(DatabaseNotification $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * @param $id
	 * @return DatabaseNotification|mixed
	 */
	public function markAsRead($id)
	{
		/** @var DatabaseNotification $notification */
		$notification = $this->getUser()->unreadNotifications()->whereKey($id)->first();
		$notification && $notification->markAsRead();

		return $notification;
	}

	/**
	 * @inheritDoc
	 */
	public function notifications()
	{
		return $this->getUser()->notifications;
	}

	/**
	 * @inheritDoc
	 */
	public function readNotifications()
	{
		return $this->getUser()->readNotifications;
	}

	/**
	 * @inheritDoc
	 */
	public function unreadNotifications()
	{
		return $this->getUser()->unreadNotifications;
	}
}
