<?php
namespace App\Repositories;

use App\Enums\AppointmentStatusType;
use App\Enums\EvaluationStatus;
use App\Helpers\Helper;
use App\Helpers\ImportHelper;
use App\Interfaces\AppointmentRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Appointment;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Carbon\Carbon;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Validation\Rule;
use Lang;

/**
 * Class AppointmentRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AppointmentRepository extends BaseRepository implements AppointmentRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	protected $filterables = ['team_id', 'status'];

	public function __construct(Appointment $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * @param $teamId
	 * @return int
	 */
	public function teamAppointmentCount($teamId, $filter = [])
	{
		$query = $this->getmainModel()
			->where('team_id', $teamId);
		if ($filter) {
			$query->where($filter);
		}

		return $query->count();
	}

	public function create($data)
	{
//		$count = $this->teamAppointmentCount($data['team_id']);
//		if ($count) {
//			throw new ResourceException(Lang::get('error.has_active_appointment'));
//		}

		$count = $this->teamAppointmentCount($data['team_id']);
		if (Helper::settings('team_max_appointment') && $count >= Helper::settings('team_max_appointment')) {
			throw new ResourceException(Lang::get('error.team_reach_max_appointment'));
		}

		if (Helper::settings('evaluation_start_date')) {
			$evalStart = Carbon::parse(Helper::settings('evaluation_start_date'));
			$startTime = Carbon::parse($data['start_time']);

			$lastEvalDay = $evalStart->copy()->addDays(2);
			if ($lastEvalDay->isAfter($startTime)) {
				$end = $lastEvalDay->startOfDay()->format('Y-m-d H:i:s');

				$count = $this->teamAppointmentCount($data['team_id'], [
					['start_time', '<', $end],
				]);

				if ($count >= Helper::settings('fist_two_day_max_eval_count', 3)) {
					throw new ResourceException(Lang::get('error.team_reach_max_appointment_for_first_two_day'));
				}
			}

			$lastEvalDay = $evalStart->copy()->addDays(2);
			if ($lastEvalDay->isBefore($startTime)) {
				$start = $lastEvalDay->startOfDay()->format('Y-m-d H:i:s');
				$end   = $lastEvalDay->endOfDay()->format('Y-m-d H:i:s');

				$count = $this->teamAppointmentCount($data['team_id'], [
					['start_time', '>=', $start],
					['start_time', '<=', $end],
				]);
				if ($count >= Helper::settings('last_day_max_eval_count', 1)) {
					throw new ResourceException(Lang::get('error.team_reach_max_appointment_for_last_day'));
				}
			}
		}

//		$count = $this->tenMinAppointmentCount($data['start_time']);
//		if ($count >= 3) {
//			throw new ResourceException(Lang::get('error.max_appointment_exceeded'));
//		}

		return $this->getMainModel()->create($data);
	}

	/**
	 * @param $date
	 * @return int
	 * @throws \Exception
	 */
	public function tenMinAppointmentCount($date)
	{
		return $this->getmainModel()
			->where('start_time', '>=',
				(new Carbon($date))->toDateTimeString())
			->where('start_time', '<',
				(new Carbon($date))->addMinutes(10)->toDateTimeString())
			->count();
	}

	public function getTeams()
	{
		return app(TeamRepositoryInterface::class)
			->getMainModel()
			->where('completed', true)
//			->whereHas('appointments', null, '<=', 0)
			->inRandomOrder()
			->get();
	}

	public function getTeamEvalNotSuccess()
	{
		return app(TeamRepositoryInterface::class)
			->getMainModel()
			->where('completed', true)
//			->whereHas('appointments', null, '<=', 0)
			->whereHas('currentEvaluation', function ($query) {
				$query->where('status', EvaluationStatus::SUCCESS);
			}, '<=', 0)
			->inRandomOrder()
			->get();
	}

	/**
	 * @param $startDate
	 * @throws \Exception
	 */
	public function autoAppointment($startDate)
	{
		$current = new Carbon($startDate);
		$current->setTime(9, 0);

		$this->removeDayAppointment($current->copy());

		$appointmentTime  = Helper::settings('appointment_time', 10);
		$appointmentLimit = Helper::settings('appointment_slot_limit', 2);

		$startTime = $current->toDateTimeString();
//		$teams     = $this->getTeams();
		$teams = $this->getTeamEvalNotSuccess();
		foreach ($teams as $team) {
			$count = $this->tenMinAppointmentCount($current);
			if ($count >= $appointmentLimit) {
				$current = $current->addMinutes($appointmentTime); //TODO add loop while error continue
			}
//			$current = $current->addMinutes(10);
			if ($current->hour == 12 && $current->minute >= 30) {
				$current->setTime(13, 30);
			}
			if ($current->hour == 18 && $current->minute >= 40) {
				$current->addDay()->setTime(9, 0);
			}

			$this->create([
				'team_id'    => $team->id,
				'start_time' => $current->format('Y-m-d H:i:00'),
				'comments'   => 'auto created',
				'created_by' => 0
			]);
		}

		$endTime = $current->toDateTimeString();

		return [
			'start_time' => $startTime,
			'end_time'   => $endTime,
			'team_count' => $teams->count()
		];
	}

	protected function removeDayAppointment(Carbon $date)
	{
		return $this->getMainModel()
			->where('start_time', '>=', $date->startOfDay()->toDateTimeString())
			->where('start_time', '<=', $date->endOfDay()->toDateTimeString())
			->delete();
	}

	/**
	 * @param $isJoined
	 */
	public function joined($isJoined)
	{
		$this->update([
			'status' => $isJoined ? AppointmentStatusType::JOINED : AppointmentStatusType::NOT_JOINED
		]);
	}

	public function appointmentNotJoined()
	{
		return $this->getMainModel()
			->where('start_time', '>=',
				Carbon::now()->startOfDay()->toDateTimeString())
			->where('start_time', '<',
				Carbon::now()->endOfDay()->toDateTimeString())
			->where('status', AppointmentStatusType::NOT_JOINED)
			->with('team')
			->get();
	}

	public function import($collection)
	{

		$rules = [
			'team_id'    => "required|unique_with:appointments,team_id,start_time",
			'start_time' => ['date'],
			'status'     => [Rule::in(AppointmentStatusType::getValues())],
			'comments'   => ['nullable', 'string'],
		];

		$map   = [
			'team_id'    => 'team_id',
			'start_time' => 'start_time',
			'status'     => 'status',
			'comments'   => 'comments',
		];

		$teams = app(TeamRepositoryInterface::class)->getAll();

		$cast = [
			'team_id' => function ($val) use ($teams) {
				$team = $teams->first(function ($item) use ($val) {
					return $item->team_id == $val;
				});
				if (!$team) {
					throw new ResourceException('team not found', [
						'team_id' => $val
					]);
				}

				return $team->id;
			}
		];

		$rows = ImportHelper::createData($collection, $rules, $map, $cast);

		return $this->getMainModel()->insert($rows);
	}
}
