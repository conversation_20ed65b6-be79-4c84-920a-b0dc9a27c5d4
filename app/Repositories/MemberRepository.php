<?php

namespace App\Repositories;

use App\Interfaces\AssetRepositoryInterface;
use App\Interfaces\MemberRepositoryInterface;
use App\Models\Member;
use App\Models\UserAsset;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\FindByMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use App\Traits\UserTrait;
use Auth;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelMedium;
use Endroid\QrCode\Label\Alignment\LabelAlignmentCenter;
use Endroid\QrCode\Label\Font\OpenSans;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

/**
 * Class MemberRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Member|mixed|Builder getMainModel()
 */
class MemberRepository extends BaseRepository implements MemberRepositoryInterface
{
	use MainModelMethodsTrait {
		create as baseCreate;
		update as baseUpdate;
	}
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;
	use UserTrait;

	public function __construct(Member $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function create($data)
	{
		$pictureId = null;
		if (array_key_exists('picture_id', $data)) {
			$pictureId = $data['picture_id'];
			unset($data['picture_id']);
		}

		$member = $this->baseCreate($data);
		$this->setMainModel($member);

		$pictureId && $this->assignPicture($pictureId);

		return $member;
	}

	public function update($data)
	{

		$pictureId = null;
		if (array_key_exists('picture_id', $data)) {
			$pictureId = $data['picture_id'];
			unset($data['picture_id']);
		}

		$member = $this->baseUpdate($data);

		$pictureId && $this->assignPicture($pictureId);

		return $member;
	}

	/**
	 * @param $assetId
	 * @return false|Model
	 */
	public function assignPicture($assetId)
	{
		/** @var AssetRepositoryInterface $assetRepo */
		$assetRepo = app(AssetRepositoryInterface::class)
			->setUser(Auth::user());
		$asset = $assetRepo->findUserAssetOrFail($assetId);

		$this->getMainModel()->picture()->delete();

		return $this->getMainModel()->picture()->save(new UserAsset([
			'asset_id' => $asset->id,
		]));
	}

	public function getTeamMembers($teamId)
	{
		return $this->getMainModel()->where('team_id', $teamId)->get();
	}

	public function getAllMembers()
	{
		return $this->getMainModel()
			->orderBy('team_id')
			->get();
	}

	public function getAllMembersFromTeamIds($teamIds)
	{
		return $this->getMainModel()
			->whereIn('team_id', $teamIds)
			->orderBy('team_id')
			->get();
	}

	public function getMemberByRole($teamId, $role)
	{
		$query = $this->getMainModel()
			->where('team_id', $teamId)
			->where('role_in_team', $role);

		if ($this->getMainModel()) {
			$query = $query->whereKeyNot($this->getMainModel()->id);
		}

		return $query->count();
	}

	/**
	 * @return string
	 */
	public function makeQRCode()
	{
		$data = $this->getMainModel()->only([
			'first_name',
			'last_name',
			'team_id',
			'identity_number',
//			'hes_code',
		]);

		$result = \Endroid\QrCode\Builder\Builder::create()
			->writer(new PngWriter())
			->writerOptions([])
			->data(json_encode($data))
			->encoding(new Encoding('UTF-8'))
			->errorCorrectionLevel(new ErrorCorrectionLevelMedium())
			->size(200)
			->margin(5)
//			->roundBlockSizeMode(new RoundBlockSizeModeMargin())
//			->logoPath(__DIR__.'/assets/symfony.png')
			->labelText($data['first_name'] . ' ' . $data['last_name'])
			->labelFont(new OpenSans(14))
			->labelAlignment(new LabelAlignmentCenter())
			->build();

		$path = '/assets/' . Str::uuid()->toString() . '.png';
		// Save it to a file
		$result->saveToFile(storage_path('app/public' . $path));

		if ($this->getMainModel()->qr_code) {
			$file = storage_path('app/public' . $this->getMainModel()->qr_code);
			file_exists($file) && unlink($file);
		}
		$this->update([
			'qr_code' => $path
		]);

		return $path;
	}

	/**
	 * @return int
	 */
	public function makeQrCodeForAllMembers($members = null)
	{
		if (!$members) {
			$members = $this->getAll();
		}
		foreach ($members as $member) {
			$this->setMainModel($member);
			$this->makeQRCode();
		}

		return $members->count();
	}

	/**
	 * @return int
	 */
	public function makeQrCodeForTeam($teamId)
	{
		$members = $this->getTeamMembers($teamId);

		return $this->makeQrCodeForAllMembers($members);
	}


	public function findMemberBy($where)
	{
		return $this->findBy($where);
	}
}
