<?php
namespace App\Repositories;

use App\Enums\PenaltyType;
use App\Helpers\ImportHelper;
use App\Interfaces\PenaltyRepositoryInterface;
use App\Interfaces\SessionTeamRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Penalty;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Class PenaltyRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Penalty|Builder getMainModel()
 */
class PenaltyRepository extends BaseRepository implements PenaltyRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	protected $filterables = ['team_id'];

	public function __construct(Penalty $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function create($data)
	{
		$model = $this->getMainModel()->create($data);
		if ($model->session_id) {
			app(SessionTeamRepositoryInterface::class)->addedPenaltyEvent($model->team_id, $model->session_id);
		}

		return $model;
	}

	public function getTeamPenalties($teamId)
	{
		return $this->getMainModel()->where('team_id', $teamId)->get();
	}

	public function export($teamId, $filePath = 'php://output')
	{
		$team        = app(TeamRepositoryInterface::class)->findOrFail($teamId);
		$reader      = IOFactory::createReader('Xlsx');
		$spreadsheet = $reader->load(storage_path('app/tubitak_penalty.xlsx'));

		$contentStarts = 11;
		$currentRow    = $contentStarts;
		$spreadsheet->getActiveSheet()
			->setCellValue('C6', $team->university_name)
			->setCellValue('C7', $team->team_name);

		foreach ($this->getTeamPenalties($teamId) as $item) {
			$spreadsheet->getActiveSheet()->insertNewRowBefore($currentRow + 1);
			$spreadsheet->getActiveSheet()
				->setCellValue('B' . $currentRow, $item->subject)
				->setCellValue('C' . $currentRow, $item->violation)
				->setCellValue('D' . $currentRow, $item->penalty >= 0 ? $item->penalty : 0)
				->setCellValue('E' . $currentRow, $item->penalty < 0 ? -$item->penalty : 0)
				->setCellValue('F' . $currentRow, $item->type)
				->setCellValue('G' . $currentRow, $item->session_id ?: 'All');
//				->setCellValue('G' . $currentRow, $item->conclusion); removed conclusion for teams

			$currentRow++;
		}
		$spreadsheet->getActiveSheet()
			->setCellValue('D' . ($currentRow), '=SUM(D11:D' . ($currentRow - 1) . ')')
			->setCellValue('E' . ($currentRow), '=SUM(E11:E' . ($currentRow - 1) . ')');

		$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');

		$writer->save($filePath);

		return $writer;
	}

	public function import($collection)
	{

		$rules = [
			'team_id'    => "required|unique_with:penalties,team_id,violation",
			'penalty'    => "required",
			'session_id' => "nullable",
			'subject'    => 'required|string',
			'violation'  => 'required|string',
			'type'       => Rule::in(PenaltyType::getValues()),
		];

		$map = [
			'team_id'    => 'team_id',
			'penalty'    => 'penalty',
			'session_id' => 'session_id',
			'subject'    => 'subject',
			'violation'  => 'violation',
			'type'       => 'type',
		];

		$teams = app(TeamRepositoryInterface::class)->getAll();

		$cast = [
			'team_id' => function ($val) use ($teams) {
				$team = $teams->first(function ($item) use ($val) {
					return $item->team_id == $val;
				});

				return $team ? $team->id : null;
			},
			'session_id' => fn($val) => empty($val) ? null : $val
		];

		$rows = ImportHelper::createData($collection, $rules, $map, $cast);

		$this->getMainModel()->insert($rows);

		return $rows;
	}

	public function videoPenaltyImport($collections)
	{
		foreach ($collections as $collection) {
			foreach ($collection as $line) {
				$teamNo = $line->get('TAKIM ID');
				if (!$teamNo) {
					continue;
				}
				$team = app(TeamRepositoryInterface::class)->findByTeamNo($teamNo);
				foreach ($line as $index => $item) {

					if (\Str::endsWith($item, 'Wh')) {
						$this->create([
							'penalty'    => (int)trim(str_replace('Wh', '', $item)),
							'team_id'    => $team->id,
							'session_id' => null,
							'subject'    => 'Video Ceza',
							'violation'  => $index,
							'type'       => PenaltyType::BEFORE,
						]);
					}
				}
			}
		}
	}

	public function truncate()
	{
		\DB::statement('TRUNCATE TABLE ' . $this->getMainModel()->getTable());
	}

}
