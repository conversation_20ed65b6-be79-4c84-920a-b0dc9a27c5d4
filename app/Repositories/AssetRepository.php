<?php
namespace App\Repositories;

use App\Enums\AssetType;
use App\Interfaces\AssetRepositoryInterface;
use App\Models\Asset;
use App\Models\UserAsset;
use App\Traits\Repository\CreateMainModel;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\UserTrait;
use Illuminate\Http\UploadedFile;
use Image;
use Storage;

/**
 * Class AssetRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class AssetRepository extends BaseRepository implements AssetRepositoryInterface
{
	use CreateMainModel;
	use FindOrFailMainModel;
	use UserTrait;

	/**
	 * AssetRepository constructor.
	 *
	 * @param Asset $mainModel
	 */
	public function __construct(Asset $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * Upload asset from request
	 *
	 * @param UploadedFile $file
	 * @return Asset
	 */
	public function upload($file)
	{
		$extension  = $file->getClientOriginalExtension();
		$uploadPath = $this->getUploadPath();
		$uniqueName = $this->getUniqueName($extension);
		$path       = $file->storeAs(
			$uploadPath, $uniqueName, ['disk' => 'public']
		);
		$type       = null;
		switch ($extension) {
			case 'mp4':
			case 'mov':
				$type = AssetType::VIDEO;
				break;
			case 'jpeg':
			case 'jpg':
			case 'png':
				$type = AssetType::IMAGE;
				break;
			default:
				$type = AssetType::FILE;
		}
		$thumbnail = $type == AssetType::IMAGE ? $this->makeThumb($path) : null;
		/** @var Asset $asset */
		$asset = $this->create([
			"path"      => $path,
			"thumbnail" => $thumbnail,
			"type"      => $type,
			"user_id"   => $this->getUser()->id,
			"size"      => number_format($file->getSize() / (1024 * 1024), 3)
		]);

		return $asset;
	}

	/**
	 * @param $path
	 * @return string
	 */
	protected function makeThumb($path)
	{
		ini_set('memory_limit','256M');
		$prefix = Storage::disk('public')->getDriver()->getAdapter()->getPathPrefix();

		$img       = Image::make($prefix . $path)->resize(260, 150, function ($constraint) {
			$constraint->aspectRatio();
		});
		$pathinfo  = pathinfo($path);
		$thumbPath = $pathinfo['dirname'] . '/' . $pathinfo['filename'] . '_thumbnail.' . $pathinfo['extension'];

		$img->save($prefix . $thumbPath);

		return $thumbPath;
	}

	/**
	 * @param $assetId
	 * @return UserAsset|mixed
	 */
	public function findUserAssetOrFail($assetId)
	{
		return $this->getUser()->assets()->findOrFail($assetId);
	}

	/**
	 * Delete an asset
	 *
	 * @param integer $id
	 * @return mixed
	 */
	public function deleteAsset($id)
	{
		return $this->getMainModel()->whereKey([
			'id'      => $id,
			'user_id' => $this->getUser()->id,
		])->delete();
	}

	/**
	 * Get Storage Path
	 *
	 * @return string
	 */
	protected function getUploadPath()
	{
		return 'assets/' . date('Y/m/d');
	}

	/**
	 * Generate random unique file name
	 *
	 * @param string $extension
	 * @param string $path
	 * @return string
	 */
	protected function getUniqueName($extension)
	{
		return md5(uniqid()) . '.' . $extension;
	}

	/**
	 * @return mixed
	 */
	public function systemFiles()
	{
		return UserAsset::where('mediable_type', 'system')
			->where('mediable_id', 0)
			->with('asset')
			->get();
	}

	/**
	 * @param $assetId
	 * @param $type
	 * @return mixed
	 */
	public function addSystemFile($assetId, $type)
	{
		return UserAsset::create([
			'asset_id'      => $assetId,
			'type'          => $type,
			'mediable_type' => 'system',
			'mediable_id'   => 0,
		]);
	}

	/**
	 * @param $id
	 * @return mixed
	 */
	public function deleteSystemFile($id)
	{
		return UserAsset::whereKey($id)->delete();
	}
}
