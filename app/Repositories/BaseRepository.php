<?php
namespace App\Repositories;

use App\Interfaces\Repository\FindMainModelInterface;
use App\Models\BaseModel;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BaseRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
abstract class BaseRepository
{
	protected $mainModel;

	/**
	 * BaseRepository constructor.
	 *
	 * @param Model $mainModel
	 */
	public function __construct(Model $mainModel)
	{
		$this->setMainModel($mainModel);
	}

	/**
	 * @return Model|Builder|BaseModel
	 */
	public function getMainModel(): Model
	{
		return $this->mainModel;
	}

	/**
	 * @param Model $mainModel
	 * @return $this
	 */
	public function setMainModel(Model $mainModel)
	{
		$this->mainModel = $mainModel;

		return $this;
	}

	/**
	 * @param Model $mainModel
	 * @return $this
	 * @see FindMainModelInterface
	 */
	public function findMainModel($id)
	{
		$this->setMainModel($this->find($id));

		return $this;
	}

	/**
	 * @param Model $mainModel
	 * @return $this
	 * @see FindMainModelInterface
	 */
	public function findMainModelOrFail($id)
	{
		$this->setMainModel($this->findOrFail($id));

		return $this;
	}
	public function getLock($name, $time = 10)
	{
		DB::select('SELECT GET_LOCK(:name,:time)', [
			'name' => $name,
			'time' => $time,
		]);
	}

	public function releaseLock($name)
	{
		DB::select('SELECT RELEASE_LOCK(:name)', [
			'name' => $name,
		]);
	}
}
