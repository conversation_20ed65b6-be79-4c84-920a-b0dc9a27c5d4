<?php

namespace App\Repositories;

use App\Enums\AppType;
use App\Enums\SchoolType;
use App\Enums\VehicleCategory;
use App\Interfaces\HesCodeServiceInterface;
use App\Interfaces\MemberRepositoryInterface;
use App\Interfaces\PromotionRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Team;
use App\Rules\RequiredMember;
use App\Rules\ValidHesCode;
use App\Traits\FiltersTrait;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use App\Traits\ValidateTrait;
use Carbon\Carbon;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\RequiredIf;

/**
 * Class TeamRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Team|Builder getMainModel()
 */
class TeamRepository extends BaseRepository implements TeamRepositoryInterface
{
	use MainModelMethodsTrait {
		update as baseUpdate;
	}
	use FindOrFailMainModel;
	use GetAllPaginateBuilder {
		getAllPaginate as baseGetAllPaginate;
	}

	use ValidateTrait;
	use FiltersTrait;

	protected $filterables = ['completed', 'id'];

	public function __construct(Team $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function getAllPaginate($query = null)
	{
		$query = $this->getMainModel()->query();
		if ($this->getFilters('sticker') !== null) {
			$query->whereHas('stickers', null,
				$this->getFilters('sticker') ? '>' : '=', 0);
		}

		return $this->baseGetAllPaginate($query);
	}

	/**
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection
	 */
	public function getTeamsWithSticker()
	{
		return $this->getMainModel()->whereHas('stickers')->get();
	}

	/**
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection
	 */
	public function getApprovedTeams()
	{
		return $this->getMainModel()->where('completed', 1)->get();
	}

	/**
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection
	 */
	public function findTeamByCaptain($captainEmail)
	{
		$query = $this->getMainModel();
//		if (AppType::isLise()) {
//			$query = $query->where('consultant_email', $captainEmail);
//		} else {
		$query = $query->where('team_leader_email', $captainEmail);

//		}

		return $query->firstOrFail();
	}

	public function findByTeamNo($teamId)
	{
		return $this->getMainModel()->where('team_id', $teamId)->first();
	}

	public function approve()
	{
		$team = $this->getMainModel();
//		$team->load(['academicAdvisor', 'driver', 'reserveDriver', 'membersInArea.hesCodeResult']);
		$team->load(['academicAdvisor', 'driver', 'reserveDriver', 'membersInArea']);
		$team->team_member_count = $team->members()->count();
		$this->giveVehicleNumber();
		$team->save();

		$this->validate([
			'team_name'         => 'required',
			'university_name'   => SchoolType::isLiseTeam($team) ? 'required' : '',
			'vehicle_name'      => 'required',
			'city_name'         => 'required',
			'vehicle_number'    => 'required|min:1|max:99',
			'team_member_count' => 'required',

//			'team_leader'       => 'required',
//			'team_leader_phone' => 'required',
//			'team_leader_email' => 'required',

//			'consultant_name'     => 'required_without:curator_name',
//			'consultant_phone'    => 'required_without:curator_phone',
//			'consultant_email'    => 'required_without:curator_email',
//			'curator_name'      => 'required_without:consultant_name',
//			'curator_phone'     => 'required_without:consultant_phone',
//			'curator_email'     => 'required_without:consultant_email',
//			'driver_name'         => 'required',
//			'driver_phone'        => 'required', //TODO open when added on impoerted team.xlsx
//			'driver_email'        => 'required',
//			'second_driver_name'  => 'required',
//			'second_driver_phone' => 'required',
//			'second_driver_email' => 'required',

			'academic_advisor' => AppType::isLise() ? new RequiredMember() : '',
//			'driver'            => new RequiredMember(),
//			'reserve_driver'    => new RequiredMember(),
//			'members_in_area.*' => new ValidHesCode(),

			'vehicle_category' => 'required',
		], $team->toArray());

		$this->update([
			'completed' => true
		]);

//		$this->makeQRCode();

		return [
			'status' => true
		];
	}

	protected function makeQRCode()
	{
		return app(MemberRepositoryInterface::class)
			->makeQrCodeForTeam($this->getMainModel()->id);
	}

	public function create($data)
	{
		$repo = app(PromotionRepositoryInterface::class);
		$team = $this->getMainModel()->create($data);

		$repo->generatePromotionForTeam($team->id);

		return $team;
	}

	/**
	 * @param Collection $collection
	 * @return |null
	 */
	public function import($collection)
	{
		$rules = [
			'team_name'         => "required|string|min:2|unique_with:teams,team_name,vehicle_name,vehicle_category",
			'university_name'   => 'string|max:255|nullable',
			'vehicle_name'      => 'string|max:255|nullable',
			'city_name'         => 'string|max:255|nullable',
			'vehicle_number'    => 'integer|nullable',
			'team_member_count' => 'integer|nullable',
			'team_leader'       => 'string|max:255|nullable',
			'team_leader_phone' => 'max:128|nullable',
			'team_leader_email' => 'string|max:255|email|nullable',
//			'consultant_name'     => 'string|max:255|nullable',
//			'consultant_phone'    => 'max:128|nullable',
//			'consultant_email'    => 'string|max:255|email|nullable',
//			'curator_name'        => 'string|max:255|nullable',
//			'curator_phone'       => 'max:128|nullable',
//			'curator_email'       => 'string|max:255|email|nullable',
//			'driver_name'         => 'string|max:255|nullable',
//			'driver_phone'        => 'max:128|nullable',
//			'driver_email'        => 'string|max:255|email|nullable',
//			'second_driver_name'  => 'string|max:255|nullable',
//			'second_driver_phone' => 'max:128|nullable',
//			'second_driver_email' => 'string|max:255|email|nullable',
			'vehicle_category'  => ['string', 'max:255', 'nullable', Rule::in(VehicleCategory::getValues())],

		];

		$maps = [
			'old' => [
				'team_name'         => 'takim_adi',
				'university_name'   => 'universite',
				'vehicle_name'      => 'arac_adi',
				'city_name'         => 'sehir',
				'vehicle_number'    => 'arac_no',
				'team_member_count' => 'kisi_sayisi',
				'team_leader'       => 'takim_kaptani_adi',
				'team_leader_phone' => 'takim_kaptani_tel',
				'team_leader_email' => 'takim_kaptani_e_posta',
//				'consultant_name'     => 'akademik_danisman_adi',
//				'consultant_phone'    => 'akademik_danisman_tel',
//				'consultant_email'    => 'akademik_danisman_e_posta',
//				'curator_name'        => 'akademik_sorumlu',
//				'curator_phone'       => 'telefon',
//				'curator_email'       => 'e_posta',
//				'driver_name'         => 'surucu_adi',
//				'driver_phone'        => 'surucu_telefon',
//				'driver_email'        => 'surucu_e_posta',
//				'second_driver_name'  => 'yedek_surucu',
//				'second_driver_phone' => 'yedek_surucu_telefon',
//				'second_driver_email' => 'yedek_surucu_e_posta',
				'vehicle_category'  => 'arac_kategorisi',
			],
			'new' => [
				'team_name'         => 'team_name',
				'team_id'           => 'team_id',
				'university_name'   => 'university_name',
				'vehicle_name'      => 'vehicle_name',
				'city_name'         => 'city_name',
				'vehicle_number'    => 'vehicle_number',
				'team_member_count' => 'number_of_team_members',
				'team_leader'       => 'team_captain_name',
				'team_leader_phone' => 'team_captain_phone',
				'team_leader_email' => 'team_captain_email',
				'school_type'       => 'school_type',
				'ttr_link'           => 'ttr',
//				'consultant_name'     => 'academic_advisor_name',
//				'consultant_phone'    => 'academic_advisor_phone',
//				'consultant_email'    => 'academic_advisor_email',
//				'curator_name'        => 'substitute_academic_advisor_name',
//				'curator_phone'       => 'substitute_academic_advisor_phone',
//				'curator_email'       => 'substitute_academic_advisor_email',
//				'driver_name'         => 'driver_name',
//				'driver_phone'        => 'driver_phone',
//				'driver_email'        => 'driver_email',
//				'second_driver_name'  => 'reserve_driver_name',
//				'second_driver_phone' => 'reserve_driver_phone',
//				'second_driver_email' => 'reserve_driver_email',
				'vehicle_category'  => 'category',
			]
		];

		$errors = null;
		$key    = array_key_exists('takim_adi', $collection->first()->first()->toArray()) ? 'old' : 'new';
		$map    = $maps[$key];
		$cast   = [
			'vehicle_number'    => function ($val) {
				return $val;
			},
			'team_id'           => function ($val) {
				return (int)$val;
			},
			'team_member_count' => function ($val) {
				return $val;
			},
			'vehicle_category'  => function ($val) {
				$val = strtolower($val);
				if ($val == VehicleCategory::EL_TR) {
					return VehicleCategory::ELECTROMOBILE;
				}

				if ($val == VehicleCategory::HY_TR) {
					return VehicleCategory::HYDROMOBILE;
				}

				return $val;
			},
		];
		foreach ($collection->slice(0, 3) as $sheetIndex => $sheet) {
			foreach ($sheet as $i => $row) {
				$data = [];
				foreach ($map as $index => $item) {
					$func = isset($cast[$index]) ? $cast[$index] : 'trim';

					$data[$index] = $row->has($item) ? $func($row[$item]) : null;
				}

				$validator = Validator::make($data, $rules);

				if ($validator->fails()) {
//				throw new ResourceException("validation.error", $validator->errors());
					$errors[$i] = ['errors' => $validator->errors(), 'data' => $data];
					continue;
				}

				$this->create($data);
//				$this->updatePartial($data);
			}
		}

		return $errors;
	}

//	public function updatePartial($data){
//		$data = array_filter($data);
//		$this->getMainModel()->newQuery()
//			->where('team_id',$data['team_id'])
//			->update($data);
//
//	}

	public function approveAllPossibleTeams()
	{
		$teams = $this->getAll();

		foreach ($teams as $team) {
			try {
				$this->setMainModel($team)->approve();
			} catch (ResourceException $e) {
			}
		}
	}

	public function attachFile($fileId, $type = null)
	{
		$this->getMainModel()->files()->create([
			'asset_id' => $fileId,
			'type'     => $type
		]);
	}

	/**
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection
	 */
	public function multiAppointmentTeam()
	{
		return $this->getMainModel()
			->whereHas('appointments', function ($query) {
				$query->where('start_time', '>=',
					Carbon::now()->startOfDay()->toDateTimeString())
					->where('start_time', '<',
						Carbon::now()->endOfDay()->toDateTimeString());
			}, '>=', '2')
			->get();
	}

	public function checkTeamHesCodes()
	{
		$hesCodes       = $this->getMainModel()->members()->select('hes_code')->pluck('hes_code');
		$hesCodeService = app(HesCodeServiceInterface::class);
		$check          = $hesCodeService->checkHesCodes($hesCodes->toArray());

		return $hesCodeService->insertHesResult($check);
	}

	protected function vehicleNumber()
	{
		$vehicle_number = random_int(1, 99);
		$isUsed         = $this->getMainModel()->where('vehicle_number', $vehicle_number)->first();
		if ($isUsed) {
			return $this->vehicleNumber();
		}

		return $vehicle_number;
	}

	/**
	 * @return $this
	 */
	public function giveVehicleNumberForAllTeams()
	{
		$query = $this->getMainModel()
			->where('vehicle_number', '<=', 0);

		$teams = $this->getAllPaginate($query); // for filter

		foreach ($teams as $team) {
			$this->setMainModel($team)->giveVehicleNumber();
		}

		return $teams;
	}

	/**
	 * @return Team|Builder
	 */
	public function giveVehicleNumber()
	{
		$team = $this->getMainModel();

		if (!$team->vehicle_number || $team->vehicle_number > 99) {
			$team->vehicle_number = $this->vehicleNumber();
		}

		return $team->save();
	}

	/**
	 * @return array
	 */
	public function checkTeam()
	{
		$errors = [];
		$team   = $this->getMainModel();

		$teamValidators = [
			'team_name'         => ['required', "string", "min:2"],
			'university_name'   => 'nullable|string|max:255',
//			'vehicle_name'      => [new RequiredIf(!AppType::isLise()), 'max:255'],
			'vehicle_name'      => ['required', 'max:255'],
			'city_name'         => 'nullable|string|max:255',
			//'team_leader'         => 'string|max:255',
			//'team_leader_phone'   => 'string|max:255',
			//'team_leader_email'   => 'string|max:255|email',
			'vehicle_category'  => ['required', 'string', 'max:255', Rule::in(VehicleCategory::getValues())],
			'vehicle_number'    => 'required|integer|min:1|max:99',
			'team_member_count' => 'integer',
			//'logo_id'             => 'integer|exists:assets,id',
		];
		$validator      = Validator::make($team->getAttributes(), $teamValidators);

		if ($validator->fails()) {
			$errors["teamValidateError"] = array("teamId" => $team["id"], "validateErrors" => $validator->errors());
		}
// TODO kayi icin gecici kapatildi

//		$memberValidate = [
//			'first_name'      => ['required', 'string',],
//			'last_name'       => ['required', 'string',],
//			'email'           => ['required', 'string', 'email'],
//			'team_id'         => ['required', 'integer', 'exists:teams,id',],
//			'role_in_team'    => ['required', 'string'],
//			'identity_number' => ['required', 'string'],
//			'phone_number'    => ['required', 'string', 'min:10'],
//			'birthday'        => ['required', 'date',],
//			'gender'          => ['required', 'string'],
//			'uniform_size'    => ['required', 'string',],
//		];

		$memberValidate = [
			'first_name'      => ['required', 'string',],
			'last_name'       => ['required', 'string',],
			'team_id'         => ['required', 'integer', 'exists:teams,id',],
			'role_in_team'    => ['required', 'string'],
			'identity_number' => ['required', 'string'],
			'phone_number'    => ['required', 'string', 'min:10'],
		];
		$memberErrors   = [];
		foreach ($team["members"] as $member) {
			$validator = Validator::make($member->toArray(), $memberValidate);

			if ($validator->fails()) {
				$memberErrors[] = array(
					"memberId"        => $member["id"],
					"memberName"      => $member["first_name"] . " " . $member["last_name"],
					"validateErrors " => $validator->errors(),
				);
			}
		}
		$errors["memberValidateErrors"] = $memberErrors;

		return $errors;
	}

}
