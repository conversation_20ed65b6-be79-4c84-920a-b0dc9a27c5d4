<?php

namespace App\Repositories;

use App\Enums\AppType;
use App\Enums\CriteriaCategory;
use App\Enums\CriteriaType;
use App\Enums\EvaluationCompatibility;
use App\Enums\EvaluationDetailFunctions;
use App\Enums\EvaluationStatus;
use App\Enums\VehicleCategory;
use App\Events\EvaluationClosed;
use App\Exports\EvalExport;
use App\Helpers\EvalHelper;
use App\Interfaces\CriteriaRepositoryInterface;
use App\Interfaces\EvaluationRepositoryInterface;
use App\Interfaces\PenaltyRepositoryInterface;
use App\Models\Evaluation;
use App\Models\EvaluationDetail;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use App\Traits\UserTrait;
use Carbon\Carbon;
use Dingo\Api\Exception\ResourceException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class EvaluationRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 *
 * @method Evaluation getMainModel()
 */
class EvaluationRepository extends BaseRepository implements EvaluationRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;
	use UserTrait;

	/**
	 * @var array
	 */
	protected $updatedDetails;

	public function __construct(Evaluation $mainModel)
	{
		\DB::enableQueryLog(); // TODO remove
		$this->defaults       = ['limit' => 100];
		$this->updatedDetails = new \Illuminate\Support\Collection();
		parent::__construct($mainModel);
	}

	protected function findTeamEvaluation($teamId)
	{
		return $this->getMainModel()
			->where('team_id', $teamId)
			->first();
	}

	public function create($data)
	{
		if ($this->findTeamEvaluation($data['team_id'])) {
			throw new ResourceException(\Lang::get('error.team_has_evaluation'));
		}

		$eval = $this->getMainModel()->create($data);
		$this->createDetail($eval);

		return $eval;
	}

	public function createAfterRaceDetails($sessionId = null)
	{
		if ($this->getMainModel()->after_race_started == $sessionId) {
			return $this->getMainModel();
		}

		$details = app(CriteriaRepositoryInterface::class)->getAfterRaceCriterias();

		$this->createDetail($this->getMainModel(), $details, $sessionId);

		$this->getMainModel()->after_race_started = $sessionId;
		$this->getMainModel()->save();

		return $this->getMainModel();
	}

	public function createAfterRaceDetailsMulti($teamIds, $sessionId)
	{
		foreach ($teamIds as $teamId) {
			$eval = $this->findTeamEvaluation($teamId);
			$this->setMainModel($eval)
				->createAfterRaceDetails($sessionId);
		}
	}

	public function createDetail($evaluation, $criterias = null, $sessionId = null)
	{

		if (!$criterias) {
			$criterias = app(CriteriaRepositoryInterface::class)->getCriterias();
		}

		$collection = new Collection();

		foreach ($criterias as $criteria) {
			if (isset($criteria->type)) {
				if (($criteria->type == CriteriaType::ELECTROMOBILE ? VehicleCategory::ELECTROMOBILE : VehicleCategory::HYDROMOBILE)
					!= $evaluation->team->vehicle_category) {
					continue;
				}
			}

			if (strlen($criteria->active) == 1) {
				$active = !$criteria->rule;
			} else {
				$rule          = $this->buildConnectedRule($criteria->active, new Collection([]));
				$triggerStatus = $this->checkConnectedRule($rule);

				$active = (int)$triggerStatus;
			}

			$collection->push([
				'evaluation_id' => $evaluation->id,
				'team_id'       => $evaluation->team_id,
				'criteria_id'   => $criteria->id,
				'compatibility' => EvaluationCompatibility::EMPTY,
				'active'        => $active,
				'after_race'    => $sessionId,
			]);
		}

		EvaluationDetail::insert($collection->toArray());

		return $evaluation;
	}

	/**
	 * @return bool|int|void
	 */
	public function close()
	{
		$check = $this->checkSticker(false);

		$result = $this->update([
			'status'    => EvaluationStatus::CLOSED,
			'remaining' => count($check['required'])
		]);
		event(new EvaluationClosed($this->getMainModel()));

		return $result;
	}

	/**
	 * @return bool|int|void
	 */
	public function reopen()
	{
		if ($this->getMainModel()->status === EvaluationStatus::ACTIVE) {
			return;
		}

		return $this->update([
			'status'   => EvaluationStatus::ACTIVE,
			'eval_day' => $this->getMainModel()->eval_day + 1
		]);
	}

	/**
	 * @param $path
	 * @return bool|\Symfony\Component\HttpFoundation\BinaryFileResponse
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function export($filePath = null)
	{
		$evaluation = $this->getMainModel();
		$exporter   = new EvalExport($evaluation);

		if ($filePath) {
			return Excel::store($exporter, $filePath);
		}

		$filePath = $evaluation->team->team_name . '_evaluation.xlsx';

		return ($exporter)->download($filePath);
	}

	public function appointmentRejectOperation($teamId)
	{
		$eval = $this->findTeamEvaluation($teamId);
		if (!$eval) {
			$eval = $this->create([
				'team_id'   => $teamId,
				'eval_date' => (new Carbon())->format('Y-m-d'),
				'status'    => EvaluationStatus::ACTIVE
			]);
		}
		$this->setMainModel($eval);

		switch ($eval->status) {
			case EvaluationStatus::FAILED:
			case EvaluationStatus::CLOSED:
				return $this->update([
					'eval_day' => $eval->eval_day + 1
				]);
			case EvaluationStatus::ACTIVE:
				return $this->update([
					'status' => EvaluationStatus::CLOSED,
				]);
		}
	}

	/**
	 * @param $id
	 * @return EvaluationDetail
	 */
	public function findOrFailDetail($id)
	{
		return EvaluationDetail::findOrFail($id);
	}

	/**
	 * @param $id
	 * @param $data
	 * @return mixed
	 */
	public function updateDetail($id, $data)
	{
		$detail = $this->findOrFailDetail($id);
		$this->findMainModel($detail->evaluation_id);
		if (!$detail->criteria->allow_value) {
			unset($data['value']);
		}

		$data['updated_by'] = $this->getUser()->id;
		$data               = $this->autoActions($detail, $data);

		$detail->update($data);

		$this->updateProgress($detail->evaluation_id);

		$this->connectedEvalDetails($detail, $data);
		$this->connectedEvalDetails($detail, $data, 'active');

		$detail->load('evaluation');
		$this->updatedDetails->add($detail);

		return $this->updatedDetails;
	}

	protected function autoActions(EvaluationDetail $detail, $data)
	{
		if ($data['compatibility'] == 0) {
			$data['penalty'] = 0;

			return $data;
		}

		$key = EvaluationCompatibility::getKeyName($data['compatibility']);

		$criteriaRule = $detail->criteria->{'allow_' . $key};
		if ($criteriaRule == '0') {
			throw new \Exception('TODODO'); // TODO
		}

		if ($criteriaRule != '+') {
			$data['penalty'] = $this->giveAutoPenalty($criteriaRule, $data['penalty'], $detail);
		} else {
			$data['penalty'] = 0;
		}

		return $data;
	}

	protected function giveAutoPenalty($criteriaStatus, $penalty, $detail = null)
	{
		$parsed = EvalHelper::parseFunction(trim($criteriaStatus));
//		if (in_array($parsed->function, EvaluationDetailFunctions::getMapKeys())) {
//			 TODO
//		}

		//Skip after race penalty if already have
		if ($detail && isset($parsed->ARParent) && $parsed->ARParent) {
			$original = $this->searchRuleInCriteria('no', $parsed->ARParent)->first();
			if ($original && $original->penalty) {
				return 0;
			}
		}

		$mul = $parsed->function == EvaluationDetailFunctions::PENALTY ? 1 : -1;

		if ($parsed->type == EvaluationDetailFunctions::StaticPenalty) {
			return $parsed->value * $mul;
		}
		if ($parsed->type == EvaluationDetailFunctions::DynamicPenalty) {
			return abs($penalty) * $mul;
		}

		return $penalty;
	}

	/**
	 * @param $evaluationId
	 */
	protected function updateProgress($evaluationId)
	{
		$total = EvaluationDetail::where('evaluation_id', $evaluationId)
			->whereHas('criteria', function ($query) {
				$query->where('is_required', 1);
			})
//			->whereNotIn('category', [
//				CriteriaCategory::After_Race_Checks,
//				CriteriaCategory::Yaris_Sonrasi_kontroller
//			]) // TODO after race implementasyonu sonraki tekrar kontrol edilecek
			->count();
		if (!$total) {
			return;
		}

		$comp = EvaluationDetail::where('evaluation_id', $evaluationId)
			->where('compatibility', EvaluationCompatibility::APPROVE)
			->whereHas('criteria', function ($query) {
				$query->where('is_required', 1);
				$query->whereNotIn('category', [
					CriteriaCategory::After_Race_Checks,
					CriteriaCategory::Yaris_Sonrasi_kontroller
				]);
			})
			->count();

		$this->findMainModelOrFail($evaluationId);
		$this->update([
			'progress' => 100 * $comp / $total
		]);
	}

	public function checkSticker($onlyRequired = true)
	{
		$eval = $this->getMainModel();

		$count = $eval->details()->count();
		if (!$count) {
			//TODO error
		}

		$query = $eval->details()
			->where(function ($query) {
				$query->where(\DB::raw('coalesce(compatibility,-1)'), '!=', EvaluationCompatibility::APPROVE);
				$query->orWhere('compatibility', EvaluationCompatibility::DISQUALIFIED);
			})
			->whereHas('criteria', function ($query) use ($onlyRequired, $eval) {
//				if (!$eval->after_race_started) {
//					$query->whereNotIn('category', [
//						CriteriaCategory::After_Race_Checks,
//						CriteriaCategory::Yaris_Sonrasi_kontroller
//					]);
//				}

				if ($onlyRequired) {
					$query->where(function ($query) {
						$query->where((new EvaluationDetail())->getTable() . '.active', 1);
						$query->where('is_required', 1);
						$query->where('compatibility', EvaluationCompatibility::EMPTY);
					});

					$query->orWhere('compatibility', EvaluationCompatibility::DISQUALIFIED);
//					$query->orWhere([
//						'is_required'   => 1,
//						'compatibility' => EvaluationCompatibility::DISQUALIFIED,
//					]);
				}
			});
		if ($eval->after_race_started) {
			$query->where(function ($query) use ($eval) {
				$query->whereNull('after_race');
				$query->orWhere('after_race', $eval->after_race_started);
			});
		}

		$failedDetails = $query->get();
		$failedDetails->load('criteria');

		$query = $eval->details()
			->where('compatibility', '=', EvaluationCompatibility::DISQUALIFIED);
//
//		if (!$eval->after_race_started) {
//			$query->whereHas('criteria', function ($query) {
//				$query->whereNotIn('category', [
//					CriteriaCategory::After_Race_Checks,
//					CriteriaCategory::Yaris_Sonrasi_kontroller
//				]);
//			});
//		}

		$disqualified = $query->get();
		$disqualified->load('criteria');

		$state = $failedDetails->count() == 0 && $disqualified->count() == 0;

		// max penalty control for domestic parts
		/*		$domesticDetails = $eval->details()
					->where('category', 'Domestic Parts')
					->whereNotNull('penalty_id')
					->get();
				$domesticDetails->load('penaltyRel');
				$totalDomPenalty = $domesticDetails->sum('penalty');*/

		return [
			'passed'       => $state,
			'required'     => $failedDetails,
			'disqualified' => $disqualified,
			'domestic'     => true,
//			'domestic' => $totalDomPenalty <= Helper::settings('max_domestic_penalty', 30)
		];
	}

	public function giveSticker()
	{
		$check             = $this->checkSticker(true);
		$uncompletedCount  = count($check['required']);
		$disqualifiedCount = count($check['disqualified']);

		if ($disqualifiedCount != 0) {
			$this->update([
				'status' => EvaluationStatus::FAILED
			]);

			return [
				'success' => false,
				'check'   => $check
			];
		}

		$eval = $this->getMainModel();
		if ($eval->status === EvaluationStatus::SUCCESS) {
			return [
				'success' => false,
				'check'   => $check,
				'message' => 'The team already has sticker'
			];
		}

		$this->update([
			'status'    => EvaluationStatus::SUCCESS,
			'remaining' => $uncompletedCount
		]);

		$penalty     = 0;
		$penaltyRepo = app(PenaltyRepositoryInterface::class);
		if ($eval->eval_day == 1) {
			if ($uncompletedCount == 0) {
				$penalty = -3; // 3 odul
			}
		}
//		else if ($eval->eval_day == 2) {
//			if ($eval->remaining <= 2 && $uncompletedCount == 0) {
//				$penalty = AppType::isLise() ? -10 : -1;
//			}
//		} else {
//			$penalty = ($eval->eval_day - 3) * (AppType::isLise() ? 10 : 0.5);
//		}

		$message = 'The team did not get prize points, remaining count:' . $uncompletedCount;
		if ($penalty) {
			$violation = 'Sticker was given in ' . $eval->eval_day . ' attempt';
			$penaltyRepo->create([
				'penalty'   => $penalty,
				'team_id'   => $eval->team_id,
				'subject'   => 'Evaluation Done',
				'violation' => $violation,
			]);
			$message = $violation . ' Team got ' . (-$penalty) . ' wh award';
		}

		return [
			'success' => true,
			'check'   => $check,
			'message' => $message
		];
	}

	public function getEvalAnalytics()
	{

		/** @var Collection $com */
		$com = EvaluationDetail::groupBy('compatibility')
			->select('compatibility', \DB::raw('count(*) as total'))
			->select('compatibility', \DB::raw('group_concat(id) as eval_ids '))
			->get();
		$com->makeHidden(['allow_penalty']);

		$val = EvaluationDetail::groupBy('criteria_id')
			->select('criteria_id', \DB::raw('count(*) as total'))
			->select('criteria_id', \DB::raw('group_concat(compatibility) as compatibilities '))
			->get();
		$val->load('criteria');

//
//		$val = EvaluationDetail::groupBy('criteria_id')
//			->select('criteria_id', \DB::raw('count(*) as total'))
//			->select('criteria_id', \DB::raw('SUM(IF(compatibility=1,1,0)) as approveCount'))
//			->get();
//		$val->load('criteria');

		return [
			'compatibilities' => $com,
			'criterias'       => $val
		];
	}

	private function connectedEvalDetails(EvaluationDetail $detail, $data, $key = 'rule')
	{
		$connectedDetails = $this->getConnectedEvalDetails($detail, $key);

		if (!$connectedDetails->count()) {
			return;
		}
		$details = $this->getMainModel()->details;

		foreach ($connectedDetails as $connected) {
			$rule = $connected->criteria[$key];
			if (!$rule) {
				continue;
			}

			$rule = $this->buildConnectedRule($rule, $details, $detail);

			$triggerStatus = $this->checkConnectedRule($rule);

			if ($key == 'active') {
				$to = [
					'active'        => (int)$triggerStatus,
					'penalty'       => 0,
					'compatibility' => EvaluationCompatibility::EMPTY
				];
			}

			if ($key == 'rule') {
				$moveTo = $this->findTriggerTarget($triggerStatus, $connected);
				if ($connected->compatibility == $moveTo) {
					continue;
				}

				$to = [
					'compatibility' => $moveTo,
					'penalty'       => 0
				];
			}

			$this->updateDetail($connected->id, $to);
		}
	}

	/**
	 * @param $rule
	 * @param $details
	 * @return array|string|string[]
	 */
	protected function buildConnectedRule($rule, $details, $currentDetail = null)
	{
//		$rule = str_replace(
//			['ise', 'KUSURLU', 'UYGUN', 'veya', 've', '='],
//			['', EvaluationDetailStatus::DEFECTIVE, EvaluationDetailStatus::APPROVE, 'or', 'and', '==',], $rule);

		$matches = [];
		preg_match_all('/#(\w{0,2}\d+\.\d+)([\s,]?)/', $rule, $matches);

//			shuffle($matches[1]);
//			sort($matches[0]);
//			sort($matches[1]);
//			$replaceIds = $details->whereIn('criteria.no', $matches[1])
//				->pluck('compatibility')->toArray();
		$i = 0;
		foreach ($matches[1] as $match) {
			$item = $details->where('criteria.no', '===', trim($match));
			if (!$item->count() && Str::startsWith($match, 'AR')) {
				$item = $details->where('criteria.no', '===', Str::replaceFirst('AR', '', $match));
			} else {
				if ($currentDetail && $currentDetail->after_race) {
					$item = $item->where('after_race', $currentDetail->after_race);
				}
			}

			$item = $item->first();

			$lastChar     = $matches[2][$i++];
			$replaceIds[] = (isset($item) && isset($item->compatibility) ?
					$item->compatibility : EvaluationCompatibility::EMPTY) . $lastChar;
		}

		$rule = str_replace($matches[0], $replaceIds, $rule);

		return $rule;
	}

	function criteriaCount($criterias, $status)
	{
		return count(array_filter($criterias, function ($val) use ($status) {
			return $val === $status;
		}));
	}

	/**
	 * @param $rule
	 * @return null
	 */
	protected function checkConnectedRule($rule)
	{
		$rule       = str_replace('criteriaCount', '$this->criteriaCount', $rule);
		$evalResult = null;
		$evalValue  = '$evalResult = (' . $rule . ');';

		try {
			eval($evalValue);
		} catch (\ParseError $e) {
			\Log::error($e);
			throw new ResourceException('invalid evaluation rule');
		}

		return $evalResult;
	}

	/**
	 * @param                  $triggerStatus
	 * @param EvaluationDetail $connected
	 * @return int
	 */
	protected function findTriggerTarget($triggerStatus, EvaluationDetail $connected)
	{
		if (!$triggerStatus) {
			return EvaluationCompatibility::EMPTY;
		}

		switch (true) {
			// Ceza (Sabit;2 Wh)
			case strlen($connected->criteria->allow_approve) > 4:
				return EvaluationCompatibility::APPROVE;
			case strlen($connected->criteria->allow_defective) > 4:
				return EvaluationCompatibility::DEFECTIVE;
			case strlen($connected->criteria->allow_disqualified) > 4:
				return EvaluationCompatibility::DISQUALIFIED;
			// +
			case $connected->criteria->allow_approve:
				return EvaluationCompatibility::APPROVE;
			case $connected->criteria->allow_defective:
				return EvaluationCompatibility::DEFECTIVE;
			case $connected->criteria->allow_disqualified:
				return EvaluationCompatibility::DISQUALIFIED;
			default:
				return EvaluationCompatibility::EMPTY;
		}
	}

	/**
	 * @param EvaluationDetail $detail
	 * @return mixed
	 */
	private function getConnectedEvalDetails(EvaluationDetail $detail, $key = 'rule')
	{
		/** @var EvaluationDetail[] $connectedDetails */
		$query = $this->getMainModel()->details()
			->whereHas('criteria', function ($query) use ($detail, $key) {
				$query->where($key, 'like', '%#' . $detail->criteria->no . '%');
			});

		if ($detail->after_race) {
			$query->where('after_race', $detail->after_race);
		}

		return $query->with('criteria')->get();
	}

	protected function searchRuleInCriteria($key, $no)
	{
		return $this->getMainModel()->details()
			->whereHas('criteria', function ($query) use ($no, $key) {
				$query->where($key, $no);
			})->with('criteria')->get();
	}

	public function getEvaluationsByStatus($statuses)
	{
		return $this->getMainModel()->whereIn('status', (array)$statuses)->get();
	}

	/**
	 * @return void
	 */
	public function sendAllEvaluationStatusMails()
	{
//		$this->getEvaluationsByStatus([EvaluationStatus::CLOSED, EvaluationStatus::SUCCESS])
		$this->getEvaluationsByStatus([EvaluationStatus::SUCCESS])
			->each(function ($evaluation) {
				event(new EvaluationClosed($evaluation));
			});
	}
}


