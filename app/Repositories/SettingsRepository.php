<?php
namespace App\Repositories;

use App\Interfaces\SettingsRepositoryInterface;
use App\Models\Settings;

/**
 * Class SettingsRepository
 *
 * @package App\Repositories
 * <AUTHOR> A<PERSON><PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class SettingsRepository extends BaseRepository implements SettingsRepositoryInterface
{
	protected $settings;

	public function __construct(Settings $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * @inheritDoc
	 */
	public function create($data)
	{
		$rows = [];
		foreach ($data as $key => $value) {
			$rows[] = $this->getMainModel()->updateOrCreate([
				'key' => $key
			], [
				'value' => $value
			]);
		}

		return $rows;
	}

	public function getAll($keys = null)
	{
		$rows   = [];
		$result = $this->getMainModel()->get();
		foreach ($result as $item) {
			if ($keys && !in_array($item->key, $keys)) {
				continue;
			}
			$rows[$item->key] = $item->value;
		}

		return $rows;
	}

	/**
	 * @param      $key
	 * @param null $default
	 * @return mixed
	 */
	public function get($key, $default = null)
	{
		if (!$this->settings) {
			$this->settings = $this->getAll();
		}

		return isset($this->settings[$key]) ? $this->settings[$key] : $default;
	}
}
