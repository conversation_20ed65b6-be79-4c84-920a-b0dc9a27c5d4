<?php
namespace App\Repositories;

use App\Enums\AppointmentStatusType;
use App\Enums\EvaluationStatus;
use App\Interfaces\StatsRepositoryInterface;
use App\Models\Appointment;
use App\Models\Evaluation;
use App\Models\Team;
use Carbon\Carbon;

/**
 * Class StatsRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class StatsRepository extends BaseRepository implements StatsRepositoryInterface
{

	public function __construct(Team $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function teamCount()
	{
		return Team::count();
	}

	public function evalSuccessCountAtOnce()
	{
		return Evaluation::where('eval_day', 1)
			->where('status', EvaluationStatus::SUCCESS)
			->count();
	}

	public function appointmentSuccessCount()
	{
		return Appointment::query()
			->where('start_time', '>=',
				Carbon::now()->startOfDay()->toDateTimeString())
			->where('start_time', '<',
				Carbon::now()->endOfDay()->toDateTimeString())
			->where('status', AppointmentStatusType::JOINED)
			->count();
	}

	public function evalSuccessCount()
	{
		return Evaluation::where('status', EvaluationStatus::SUCCESS)
			->count();
	}

	public function stats()
	{
		return [
			'team_count'           => $this->teamCount(),
			'eval_success_at_once' => $this->evalSuccessCountAtOnce(),
			'appointment_joined'   => $this->appointmentSuccessCount(),
			'sticker_ready'        => $this->evalSuccessCount(),
		];
	}


	public function appointmentNotJoined()
	{
		return Appointment::query()
			->where('start_time', '>=',
				Carbon::now()->startOfDay()->toDateTimeString())
			->where('start_time', '<',
				Carbon::now()->endOfDay()->toDateTimeString())
			->where('status', AppointmentStatusType::NOT_JOINED)
			->get();
	}

}
