<?php
namespace App\Repositories;

use Actuallymab\LaravelComment\Contracts\Commentable;
use App\Interfaces\CommentRepositoryInterface;
use App\Traits\Repository\DeleteMainModel;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\UserTrait;
use Illuminate\Database\Eloquent\Builder;
use Takdeniz\LikableComment\Models\Comment;

/**
 * Class CommentRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Comment|Builder getMainModel()
 */
class CommentRepository extends BaseRepository implements CommentRepositoryInterface
{
	use UserTrait;
	use FindOrFailMainModel;
	use DeleteMainModel;


	public function __construct(Comment $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * @inheritDoc
	 */
	public function getComments(Commentable $topic)
	{
		return $topic->comments()->paginate();
	}

	/**
	 * @inheritDoc
	 */
	public function comment(Commentable $topic, $comment)
	{
		$comment = $this->getUser()->comment($topic, $comment);

		return $comment;
	}

	/**
	 * @inheritDoc
	 */
	public function like()
	{
		return $this->getUser()->like($this->getMainModel());
	}

	/**
	 * @return mixed
	 */
	public function dislike()
	{
		return $this->getUser()->dislike($this->getMainModel());
	}

	/**
	 * @return mixed
	 */
	public function findMineOrFail($id)
	{
		return $this->getUser()
			->comments()
			->findOrFail($id);
	}

}
