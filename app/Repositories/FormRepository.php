<?php

namespace App\Repositories;

use App\Interfaces\FormRepositoryInterface;
use App\Models\Form;
use App\Models\UserAsset;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class FormRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Form|Builder getMainModel()
 */
class FormRepository extends BaseRepository implements FormRepositoryInterface
{
	use MainModelMethodsTrait {
		create as baseCreate;
		update as baseUpdate;
	}
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	public function __construct(Form $mainModel)
	{
		parent::__construct($mainModel);
	}

	public function create($data)
	{
		$attachments = null;
		if (array_key_exists('attachments', $data)) {
			$attachments = $data['attachments'];
			unset($data['attachments']);
		}

		$form = $this->baseCreate($data);
		$this->setMainModel($form);

		$this->assignAttachment($attachments);

		return $form;
	}

	protected function assignAttachment($attachments)
	{

		foreach ((array)$attachments as $index => $attachment) {
			$this->getMainModel()->attachments()->save(new UserAsset([
				'asset_id' => $attachment['asset_id'],
				'type'     => $attachment['type'],
			]));
		}

	}

	public function update($data)
	{
		$attachments = null;
		if (array_key_exists('attachments', $data)) {
			$attachments = $data['attachments'];
			unset($data['attachments']);
		}

		$form = $this->baseUpdate($data);

		$this->assignAttachment($attachments);
		return $form;

	}

}
