<?php
namespace App\Repositories;

use App\Helpers\Helper;
use App\Interfaces\PromotionRepositoryInterface;
use App\Interfaces\TeamRepositoryInterface;
use App\Models\Promotion;
use App\Models\Team;
use App\Traits\Repository\FindOrFailMainModel;
use App\Traits\Repository\GetAllPaginateBuilder;
use App\Traits\Repository\MainModelMethodsTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use PhpOffice\PhpSpreadsheet\IOFactory;
use ZipArchive;

/**
 * Class PromotionRepository
 *
 * @package App\Repositories
 * <AUTHOR> Akdeniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 * @method Team|Builder getMainModel()
 */
class PromotionRepository extends BaseRepository implements PromotionRepositoryInterface
{
	use MainModelMethodsTrait;
	use FindOrFailMainModel;
	use GetAllPaginateBuilder;

	protected $filterables = ['team_id'];

	public function __construct(Promotion $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * @param $teamId
	 * @return bool
	 */
	public function generatePromotionForTeam($teamId)
	{
		$promSettings = Helper::settings('promotions');

		$data = [];
		foreach ($promSettings as $element) {
			$data[] = [
				'name'    => $element,
				'team_id' => $teamId
			];
		}

		return $this->getMainModel()->insert($data);
	}

	/**
	 * @param $data
	 * @return bool
	 */
	public function multiInsert($data)
	{
		$elements = [];
		foreach ($data['name'] as $name) {
			$elements[] = [
				'name'    => $name,
				'team_id' => $data['team_id']
			];
		}

		return $this->getMainModel()->insert($elements);
	}

	/**
	 * @param $teamId
	 * @return Builder[]|\Illuminate\Database\Eloquent\Collection
	 */
	public function getTeamPromotions($teamId)
	{
		return $this->getMainModel()->where('team_id', $teamId)->get();
	}

	/**
	 * @param $teamId
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function export($teamId)
	{
		$team     = app(TeamRepositoryInterface::class)->findOrFail($teamId);
		$filename = 'promotion_' . $team->team_name . '.xlsx';

		$file = storage_path('app/downloads/' . $filename);
		\File::exists(dirname($file)) || \File::makeDirectory(dirname($file));

		$this->toExcel($team, $file);

		return response()
			->download($file, basename($file))
			->deleteFileAfterSend(true);
	}

	/**
	 * @param $team
	 * @param $stream
	 * @return mixed
	 * @throws \PhpOffice\PhpSpreadsheet\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
	 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
	 */
	public function toExcel($team, $stream)
	{
		$reader      = IOFactory::createReader('Xlsx');
		$spreadsheet = $reader->load(storage_path('app/tubitak_promotions.xlsx'));

		$contentStarts = 11;
		$currentRow    = $contentStarts;
		$spreadsheet->getActiveSheet()
			->setCellValue('C6', $team->university_name)
			->setCellValue('C7', $team->team_name);

		foreach ($this->getTeamPromotions($team->id) as $item) {
			$spreadsheet->getActiveSheet()->insertNewRowBefore($currentRow + 1);
			$spreadsheet->getActiveSheet()
				->setCellValue('B' . $currentRow, $item->name)
				->setCellValue('C' . $currentRow, '√');
			$currentRow++;
		}

		$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');

		return $writer->save($stream);
	}

	public function exportAllTeamsPromotions()
	{
		$teams = app(TeamRepositoryInterface::class)->getAll();
		$files = [];
		foreach ($teams as $team) {
			$file = storage_path('app/promotions/promotion_' . $team->team_name . '.xlsx');
			$this->toExcel($team, $file);
			$files[] = $file;
		}

//		if (!$files) {
//			return '';
//		}

		return $this->exportAllPromotionsAsZip($files, storage_path("app/downloads/promotions.zip"));
	}

	public function exportAllPromotionsAsZip($files, $archiveFile)
	{
//		$files = glob(storage_path("app/promotions/*.xlsx"));
//		$archiveFile = storage_path("app/downloads/promotions.zip");
		$archive = new ZipArchive();

		\File::exists(dirname($archiveFile)) || \File::makeDirectory(dirname($archiveFile));

		// Check if the archive could be created.
		if ($archive->open($archiveFile, ZipArchive::CREATE | ZipArchive::OVERWRITE)) {
			// Loop through all the files and add them to the archive.
			foreach ($files as $file) {
				if ($archive->addFile($file, basename($file))) {
					// Do something here if addFile succeeded, otherwise this statement is unnecessary and can be ignored.
					continue;
				} else {
					throw new Exception("File [`{$file}`] could not be added to the zip file: " . $archive->getStatusString());
				}
			}

			// Close the archive.
			if ($archive->close()) {
				// Archive is now downloadable ...
				return response()->download($archiveFile, basename($archiveFile))->deleteFileAfterSend(false);
			} else {
				throw new Exception("Could not close zip file: " . $archive->getStatusString());
			}
		} else {
			throw new Exception("Zip file could not be created: " . $archive->getStatusString());
		}
	}
}
