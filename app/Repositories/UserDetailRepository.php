<?php
namespace App\Repositories;

use App\Enums\GenderType;
use App\Interfaces\AssetRepositoryInterface;
use App\Interfaces\UserDetailRepositoryInterface;
use App\Models\User;
use App\Models\UserDetail;
use App\Traits\Repository\CreateMainModel;
use App\Traits\Repository\UpdateMainModel;
use App\Traits\UserTrait;

/**
 * Class UserDetailRepository
 *
 * @package App\Repositories
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UserDetailRepository extends BaseRepository implements UserDetailRepositoryInterface
{
	use UserTrait {
		setUser as parentSetUser;
	}
	use UpdateMainModel;
	use CreateMainModel;

	/**
	 * UserSettingRepository constructor.
	 *
	 * @param UserDetail $mainModel
	 */
	public function __construct(UserDetail $mainModel)
	{
		parent::__construct($mainModel);
	}

	/**
	 * @param User $user
	 * @return $this
	 */
	public function setUser(User $user): self
	{
		$user->details && $this->setMainModel($user->details);

		return $this->parentSetUser($user);
	}

	/**
	 * @return UserDetail|mixed
	 */
	public function get()
	{
		return $this->getUser()->details;
	}

	/**
	 * @param $data
	 * @return mixed
	 */
	protected function userDetailProcess($data, $create = false)
	{
		$data['user_id'] = $this->getUser()->id;

		if (array_key_exists('avatar', $data)) {
			$assetRepo = app(AssetRepositoryInterface::class);
			$asset     = $assetRepo->uploadFromURL($data['avatar']);
			if ($asset) {
				$data["picture_id"] = $asset->id;
			}
			unset($data['avatar']);
		}

		if ($create) {
			$data["timezone"] = isset($data['timezone']) ?
				$data['timezone'] : \Config::get('settings.timezone');
		}

//		if (isset($data['mobile_number'])) {
//			$data["mobile_number"] = (string)PhoneNumber::make($data['mobile_number']);
//		}

		if (isset($data['gender'])) {
			$data['gender'] = GenderType::get($data['gender']);
		}

		return $data;
	}

	/**
	 * @param $userDetails
	 * @return mixed
	 */
	public function createUserDetails($userDetails)
	{
		$userSettingData = $this->userDetailProcess($userDetails, true);

		$settings = $this->create($userSettingData);
		$this->getUser()->setRelation('settings', $settings);

		return $settings;
	}

	/**
	 * @param $userDetails
	 * @return mixed
	 */
	public function updateUserDetails($userDetails)
	{
		$userDetailProcessed = $this->userDetailProcess($userDetails);

		return $this->update($userDetailProcessed);
	}

}
