<?php
namespace App\Transformers;

use Actuallymab\LaravelComment\Models\Comment;
use League\Fractal\TransformerAbstract;

/**
 * Class UserProfileTransformer
 *
 * @package App\Transformers
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CommentsTransformer extends TransformerAbstract
{

	protected $defaultIncludes = [
		'user'
	];

	/**
	 * @param Comment $comment
	 * @return array
	 */
	public function transform(Comment $comment)
	{

		$comment->makeHidden([
			'commented_id',
			'commented_type',
			'commentable_id',
			'commentable_type',
		]);

		$array          = $comment->toArray();

		return $array;
	}

	public function includeUser(Comment $comment)
	{
		return $this->item($comment->commented, new UserLabelTransformer(), false);
	}
}
