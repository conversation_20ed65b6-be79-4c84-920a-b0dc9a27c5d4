<?php
namespace App\Transformers;

use Illuminate\Database\Eloquent\Model;
use League\Fractal\TransformerAbstract;

/**
 * Class GeneralTransformer
 *
 * @package App\Transformers
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class GeneralTransformer extends TransformerAbstract
{

	/**
	 * @param $model
	 * @return array
	 */
	public function transform($model)
	{
		if ($model instanceof Model) {
			$model->makeHidden([
				'deleted_at',
				'created_by',
				'updated_by',
				'deleted_by',
			]);

			return $model->toArray();
		}

		return (array)$model;
	}
}
