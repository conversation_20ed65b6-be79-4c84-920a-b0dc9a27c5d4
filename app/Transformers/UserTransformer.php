<?php
namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;

/**
 * Class UserProfileTransformer
 *
 * @package App\Transformers
 * <AUTHOR> A<PERSON><PERSON>iz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UserTransformer extends TransformerAbstract
{

	/**
	 * @param User $user
	 * @return array
	 */
	public function transform(User $user)
	{
		$user->append('role', 'profile_completed'/*, 'medias'*/);
		$user->makeHidden(['userAssets']);
		$user->wallet && $user->wallet->setVisible(['balance']);

		return $user->toArray();
	}
}
