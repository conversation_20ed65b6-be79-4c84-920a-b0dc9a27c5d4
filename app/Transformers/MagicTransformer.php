<?php
namespace App\Transformers;

use App\Models\BaseModel;
use League\Fractal\Resource\Item;
use League\Fractal\TransformerAbstract;

/**
 * Class MagicTransformer
 *
 * @package App\Transformers
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class MagicTransformer extends TransformerAbstract
{
	/**
	 * @return array
	 */
	public function getAppends(): array
	{
		return $this->appends;
	}

	/**
	 * @param array $appends
	 * @return $this
	 */
	public function setAppends(array $appends)
	{
		$this->appends = $appends;

		return $this;
	}

	/**
	 * @return array
	 */
	public function getHidden(): array
	{
		return $this->hidden;
	}

	/**
	 * @param array $hidden
	 * @return $this
	 */
	public function setHidden(array $hidden)
	{
		$this->hidden = $hidden;

		return $this;
	}

	/**
	 * @var array
	 */
	protected $appends;
	/**
	 * @var array
	 */
	protected $hidden;

	public function __construct($includes = [], $appends = [], $hidden = [])
	{
		$this->defaultIncludes = array_merge($this->defaultIncludes, $includes);
		$this->appends         = $appends;
		$this->hidden          = $hidden;
	}

	/**
	 * @var array
	 */
	protected $defaultIncludes = [
	];

	/**
	 * @param $item
	 * @return array
	 */
	public function transform($item)
	{
		if ($item instanceof BaseModel) {
			if ($this->appends) {
				$item->append($this->appends);
			}
			if ($this->hidden) {
				$item->makeHidden($this->hidden);
			}

			return $item->toArray();
		}

		return $item;
	}

	/**
	 * @param $item
	 * @return Item
	 */
	public function includeUser($item)
	{
		return $this->item($item->user, new UserTransformer(), false);
	}
}
