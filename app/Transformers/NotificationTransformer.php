<?php
namespace App\Transformers;

use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;

/**
 * Class NotificationTransformer
 *
 * @package App\Transformers
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class NotificationTransformer extends TransformerAbstract
{

	/**
	 * @param DatabaseNotification $notification
	 * @return array
	 */
	public function transform(DatabaseNotification $notification)
	{
		$notification->makeHidden([
			"type",
			"notifiable_type",
			"notifiable_id",
			"updated_at",
		]);

		return $notification->toArray();
	}
}
