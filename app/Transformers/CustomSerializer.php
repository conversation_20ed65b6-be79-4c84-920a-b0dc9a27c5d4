<?php
namespace App\Transformers;

use League\Fractal\Serializer\ArraySerializer;

/**
 * Class CustomSerializer
 *
 * @package App\Transformers
 * <AUTHOR> A<PERSON>deniz <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class CustomSerializer extends ArraySerializer
{
	public function collection($resourceKey, array $data)
	{
		if ($resourceKey === false) {
			return $data;
		}

		return [$resourceKey ?: 'data' => $data];
	}

	public function item($resourceKey, array $data)
	{
		if ($resourceKey === false) {
			return $data;
		}

		return [$resourceKey ?: 'data' => $data];
	}
}
