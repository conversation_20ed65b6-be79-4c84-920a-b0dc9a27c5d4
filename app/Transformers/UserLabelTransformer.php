<?php
namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;

/**
 * Class UserLabelTransformer
 *
 * @package App\Transformers
 * <AUTHOR> <<EMAIL>>
 * @version 0.1
 * @since   0.1
 */
class UserLabelTransformer extends TransformerAbstract
{

	/**
	 * @param User $user
	 * @return array
	 */
	public function transform(User $user = null)
	{
		if (!$user) {
			return [];
		}

		$user->makeHidden([
			'userAssets',

			"email",
			"email_verified_at",
			"created_at",
			"updated_at",
			"deleted_at",
		]);

		return $user->toArray();
	}
}
