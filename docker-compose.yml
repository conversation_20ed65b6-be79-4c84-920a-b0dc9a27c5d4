version: '2'

services:
  app:
    image: takdeniz/php-fpm-laravel
    build:
      context: ./docker/php-app
    volumes:
      - ./:/var/www/
      - ./:/var/www/kutup/
      - ./.env.kutup:/var/www/kutup/.env
    networks:
      - code-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
  node:
    image: laravel-node
    build:
      context: .
      dockerfile: node.dockerfile
    command: 'bash -c "yarn install && yarn run production"'
    volumes:
      - ./:/var/www/
    networks:
      - code-network


  nginx:
    image: nginx:1.23-alpine
    container_name: nginx
    restart: unless-stopped
    ports:
      - 80:80
      - 443:443
      - 7080:7080
    volumes:
      - ./:/var/www
      - ./:/var/www/kutup/
      - ./.env.kutup:/var/www/kutup/.env
      - ./docker/nginx:/etc/nginx/conf.d
      - /var/log/nginx:/var/log/nginx
    networks:
      - code-network

networks:
  code-network:
    driver: bridge
