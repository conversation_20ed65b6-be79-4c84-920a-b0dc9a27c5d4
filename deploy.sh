#!/usr/bin/env bash

echo '=========================================================================='
date
# Change to the project directory
#cd $FORGE_SITE_PATH

FORGE_SITE_BRANCH="master"

# Pull the latest changes from the git repository
# git reset --hard
# git clean -df
git pull origin $FORGE_SITE_BRANCH

# Turn on maintenance mode
#php artisan down || true

# Install/update composer dependecies
#composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
#php composer.phar install --no-interaction --prefer-dist --optimize-autoloader --no-dev

###php composer.phar install --no-interaction --prefer-dist --optimize-autoloader

# Restart FPM
#( flock -w 10 9 || exit 1
#    echo 'Restarting FPM...'; sudo -S service $FORGE_PHP_FPM reload ) 9>/tmp/fpmlock

# Run database migrations
php artisan migrate --force

# Clear caches
#php artisan cache:clear

# Clear expired password reset tokens
#php artisan auth:clear-resets

# Clear and cache routes
#php artisan route:cache

# Clear and cache config
#php artisan config:cache

# Clear and cache views
#php artisan view:cache

# Install node modules
# npm ci

# Build assets using Laravel Mix
# npm run production

# Turn off maintenance mode
#php artisan up

#*/10 * * * * cd /var/www/html/eclise && bash deploy.sh
#*/10 * * * * cd /var/www/html/eculus && bash deploy.sh

sh ./run.sh
