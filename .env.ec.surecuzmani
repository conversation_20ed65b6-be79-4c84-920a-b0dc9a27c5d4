APP_NAME='Tubitak Efficiency Challenge'
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=https://ec.surecuzmani.com

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ec_international
DB_USERNAME=ec_international
DB_PASSWORD=7G26jjr6$

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=cookie
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.eu.sparkpostmail.com
MAIL_PORT=587
MAIL_USERNAME=SMTP_Injection
MAIL_PASSWORD=pass
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS='hello@localhost'
MAIL_FROM_NAME='Efficiency Challenge'

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


API_DEFAULT_FORMAT=json
API_STANDARDS_TREE=vnd
API_SUBTYPE=myapi
API_DOMAIN=ec.surecuzmani.com
#API_PREFIX=api
API_VERSION=v1
API_NAME="EC"
API_STRICT=false

JWT_SECRET=jrGhpDtesgvgGrtXabvLQq2T5xoFNDwrHTurjXshiKIKdTUJ7GDJLTqCXiOPRL0q
JWT_TTL=600

VERIFY_EMAIL_WRAPPER='https://ec.surecuzmani.com/verify-email/%url%'
RESET_PASSWORD_WRAPPER='https://ec.surecuzmani.com/reset-password/%url%'
APP_TYPE='normal'
#APP_TYPE='lise'

HES_SERVICE_WSDL=https://wsbroker.tubitak.gov.tr/wsbroker/TBTKHESKodServisFW?wsdl
HES_SERVICE_NS=http://tr.gov.tubitak.wsbroker.tbtkheskodservicefw
HES_SERVICE_USERNAME=EC_HES_WS_USER
HES_SERVICE_PASSWORD=PASSWORD_HERE
HES_SERVICE_ORIGIN=EC_USER
HES_SERVICE_RANDOMIZER=9io7eotn08jr25z70oxjsw011
HES_SERVICE_TOKEN=?
z
